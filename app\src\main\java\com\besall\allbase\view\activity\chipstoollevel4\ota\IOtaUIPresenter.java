package com.besall.allbase.view.activity.chipstoollevel4.ota;

import android.app.Activity;
import android.content.Context;

import com.bes.bessdk.service.base.BesServiceConfig;
import com.bes.bessdk.service.base.BesServiceListener;

public interface IOtaUIPresenter {
    void pickDecice(OtaUIActivity context, int scan);

    void connectDevice(BesServiceConfig serviceConfig, BesServiceListener listener, Context context, int index);

    void connectCustomerDialDevice(BesServiceConfig serviceConfig, BesServiceListener listener, Context context);

    void startCustomerDialTransfer(byte[] oldData, byte[] data, int type, int isIncremental, byte[] param);

    void disconnectCustomerDialService();

    void onPickOtaFile(String path, int index);

    void startOta(OtaUIActivity context, int breakpoint, int index);

    void stopOta();

    void goToSettingActivity(OtaUIActivity activity);
}
