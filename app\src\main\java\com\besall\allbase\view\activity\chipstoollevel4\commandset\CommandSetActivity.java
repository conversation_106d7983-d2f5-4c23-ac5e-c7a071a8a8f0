package com.besall.allbase.view.activity.chipstoollevel4.commandset;

import static com.bes.bessdk.BesSdkConstants.BES_CONNECT_ERROR;
import static com.bes.bessdk.service.commandset.CommandSetConstants.*;
import static com.besall.allbase.bluetooth.BluetoothConstants.Scan.BES_SCAN_RESULT;

import android.Manifest;
import android.bluetooth.BluetoothDevice;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.graphics.Color;
import android.media.MediaPlayer;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.method.ScrollingMovementMethod;
import android.text.style.ForegroundColorSpan;
import android.util.Log;
import android.view.MenuItem;
import android.view.MotionEvent;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.RadioGroup;
import android.widget.ScrollView;
import android.widget.TextView;

import androidx.core.app.ActivityCompat;

import com.bes.bessdk.BesSdkConstants;
import com.bes.bessdk.connect.BTService;
import com.bes.bessdk.scan.BtHeleper;
import com.bes.bessdk.service.base.BesServiceConfig;
import com.bes.bessdk.service.base.BesServiceListener;
import com.bes.bessdk.utils.ArrayUtil;
import com.bes.bessdk.utils.SPHelper;
import com.bes.sdk.device.HmDevice;
import com.bes.sdk.utils.DeviceProtocol;
import com.besall.allbase.R;
import com.besall.allbase.bluetooth.BluetoothConstants;
import com.besall.allbase.common.Constants;
import com.besall.allbase.common.utils.ActivityUtils;
import com.besall.allbase.common.utils.FileUtils;
import com.besall.allbase.view.activity.chipstoollevel4.ota.OtaUIActivity;
import com.besall.allbase.view.base.BaseActivity;
import com.suke.widget.SwitchButton;

import java.io.Serializable;
import java.util.Timer;
import java.util.TimerTask;

/**
 * <AUTHOR>
 * @time $ $
 */
public class CommandSetActivity extends BaseActivity<ICommandSetActivity, CommandSetPresenter> implements ICommandSetActivity, BesServiceListener, View.OnClickListener, MyScrollViewListener, RadioGroup.OnCheckedChangeListener, SwitchButton.OnCheckedChangeListener, ICommandSetPresenter.CheckMicStateListener {
    private static CommandSetActivity instance;
    public String cur_title = "COMMAND SET";
    BluetoothDevice mDevice;
    HmDevice mHmDevice;
    BesServiceConfig mServiceConfig;
    private Button pick_device;
    private Button pick_device_ble;
    private Button connect_device;
    private TextView device_address;
    private TextView device_name;
    private Button edit_cutomercmd;

    private SwitchButton switchButton_in_ear_detection_left;
    private SwitchButton switchButton_in_ear_detection_right;
    private TextView text_in_ear_detection_left;
    private TextView text_in_ear_detection_right;

    private byte curEarbudsClickTimes = BUTTON_SETTINGS_CONTROL_CLICK;
    private byte curEarbudsClickType = BUTTON_SETTINGS_CONTROL_LAST_MUSIC;
    private RadioGroup earbuds_click_func_0;
    private RadioGroup earbuds_click_func_1;
    private RadioGroup earbuds_click_func_2;
    private RadioGroup earbuds_click_func_3;

    private Button factory_reset_cmd_set;
    private Button play;
    private Button pause;
    private Button next;
    private Button prev;
    private Button button_get_left_battery;
    private TextView text_left_battery;
    private Button button_get_right_battery;
    private TextView text_right_battery;
    private Button fit_test;
    private SwitchButton switchButton_fit_test;
    private TextView fit_text;
    private Button eq_test;
    private EditText eq_text;
    private EditText command_set_receive_data;

    private RadioGroup eq_basetype;
    private byte curEqBaseType = 0x00;
    private Button disconnect;
//    private MyScrollView scrollview_0;
//    private MyScrollView scrollview_1;
//    private MyScrollView scrollview_2;
//    private MyScrollView scrollview_3;
//    private MyScrollView scrollview_4;
//    private MyScrollView scrollview_5;
//    private MyScrollView scrollview_6;
//    private MyScrollView scrollview_7;
//    private MyScrollView scrollview_8;
//    private MyScrollView scrollview_9;

    private Button get_bt_state, start_ota_btn;
    private TextView bt_state_text;
    private Button get_spp_state;
    private TextView spp_state_text;
    private Button check_mic_state;
    private TextView mic_state_text;
    private Button check_left_speaker;
    private Button check_right_speaker;

    private TextView button_state_1, button_state_2, button_state_3, button_state_4, button_state_5, button_state_6, button_state_7, button_state_8;
    private Button earbuds_click_left, earbuds_click_right, earbuds_double_click_left, earbuds_double_click_right, earbuds_triple_click_left, earbuds_triple_click_right, earbuds_long_press_left, earbuds_long_press_right;
    private TextView command_set_current_product_model, command_set_current_version;
    private RadioGroup eq_switch_type;
    private RadioGroup dolby_switch_type;
    private Button dolby_type_natual;
    private Button dolby_type_movie;

    private TextView dolby_switch_title;
    private int curDolbyType = 0;//0无状态 1Natual 2Movie

    private LinearLayout linear_dolby_state;
    private LinearLayout linear_bes_spatial_state;
    private LinearLayout linear_mimi_state;
    private LinearLayout linear_ceva_state;
    private LinearLayout linear_anc_state;
    private RadioGroup bes_spatial_switch_type;
    private RadioGroup mimi_switch_type;
    private RadioGroup ceva_switch_type;

    private RadioGroup regulate_anc_type;

    private RadioGroup mimi_switch_preset;
    private RadioGroup mimi_switch_intensity;
    private boolean isReceiveButtonCmd = false;

    private Timer getSppStatusTimer;
    private TimerTask getSppStatusTimerTask;

    private boolean hasGetSppStatus = false;
    private String curModelName = "";

    @Override
    protected CommandSetPresenter createPresenter() {
        return new CommandSetPresenter();
    }

    @Override
    protected void initBeforeSetContent() {
        mServiceConfig = new BesServiceConfig();
    }

    @Override
    protected int getContentViewId() {
        return R.layout.act_connect;
    }

    @Override
    protected void bindView() {
        pick_device = (Button)findViewById(R.id.pick_device);
        pick_device_ble = (Button)findViewById(R.id.pick_device_ble);
        connect_device =(Button)findViewById(R.id.connect_device);
        device_address = (TextView) findViewById(R.id.device_address);
        device_name = (TextView) findViewById(R.id.device_name);
        edit_cutomercmd = (Button)findViewById(R.id.edit_cutomercmd);

        logV = (TextView) findViewById(R.id.logV);
        done = (Button) findViewById(R.id.done);
        loginfo = (View) findViewById(R.id.loginfo);

        loadanimdrawable();
    }

    @Override
    protected void initView() {
        inittoolbar(cur_title);
        pick_device.setOnClickListener(instance);
        pick_device_ble.setOnClickListener(instance);
        pick_device_ble.setVisibility(View.VISIBLE);
        connect_device.setOnClickListener(instance);
        edit_cutomercmd.setVisibility(View.INVISIBLE);
        edit_cutomercmd.setOnClickListener(instance);

        scr_policy = (ScrollView)findViewById(R.id.scr_policy);

        tv_title.setOnClickListener(instance);
        done.setOnClickListener(instance);

        logV.setMovementMethod(ScrollingMovementMethod.getInstance());
        scr_policy.setOnTouchListener(new View.OnTouchListener() {
            @Override
            public boolean onTouch(View v, MotionEvent event) {
                v.getParent().requestDisallowInterceptTouchEvent(true);
                return false;
            }
        });

        if (ActivityCompat.checkSelfPermission(instance, Manifest.permission.RECORD_AUDIO) != PackageManager.PERMISSION_GRANTED) {
            ActivityCompat.requestPermissions(instance, new String[]{Manifest.permission.RECORD_AUDIO}, 1);
            return;
        }

//        initlayout();
    }

    @Override
    protected void setInstance() {
        instance = this;
    }

    @Override
    protected void removeInstance() {
        instance = null;
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == BluetoothConstants.Scan.REQUEST_CODE_SCAN) {
            onPickDevice(resultCode, data);
        }
    }

    private void onPickDevice(int resultCode, Intent data) {
        if (resultCode == RESULT_OK) {
            mHmDevice = (HmDevice) data.getSerializableExtra(BES_SCAN_RESULT);
            mDevice = BtHeleper.getBluetoothAdapter(instance).getRemoteDevice(mHmDevice.getPreferredProtocol() == DeviceProtocol.PROTOCOL_BLE ? mHmDevice.getBleAddress() : mHmDevice.getDeviceMAC());

            Log.i(TAG, "onPickDevice: " + mDevice.getName());
            Log.i(TAG, "onPickDevice: " + mDevice.getAddress());
            device_address.setText(mDevice.getAddress());
            mServiceConfig.setDevice(mHmDevice);
            String name = mDevice.getName();
            SpannableString ss = new SpannableString(name);
            BesSdkConstants.BesConnectState state = BTService.getDeviceConnectState(instance, mServiceConfig);
            Log.i(TAG, "onPickDevice: -------" + state);
            if (state == BesSdkConstants.BesConnectState.BES_CONNECT) {
                ss.setSpan(new ForegroundColorSpan(Color.rgb(103, 200, 77)), 0, name.length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
            }
            device_name.setText(ss);
        }
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        switch (item.getItemId()) {
            case android.R.id.home:
                finish();
                break;
        }
        return super.onOptionsItemSelected(item);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.pick_device:
                mPresenter.pickDecice(instance, BluetoothConstants.Scan.SCAN_SPP);
                connect_device.setVisibility(View.VISIBLE);
                mServiceConfig.setDeviceProtocol(DeviceProtocol.PROTOCOL_SPP);
                mServiceConfig.setServiceUUID(BesSdkConstants.BES_SPP_CONNECT);
                break;
            case R.id.pick_device_ble:
//                initlayout();

                mPresenter.pickDecice(instance, BluetoothConstants.Scan.SCAN_BLE);
                connect_device.setVisibility(View.VISIBLE);
                mServiceConfig.setDeviceProtocol(DeviceProtocol.PROTOCOL_BLE);
                mServiceConfig.setServiceUUID(BesSdkConstants.BES_TOTA_SERVICE_OTA_UUID);
                mServiceConfig.setCharacteristicsUUID(BesSdkConstants.BES_TOTA_CHARACTERISTI_OTA_UUID);
                mServiceConfig.setDescriptorUUID(BesSdkConstants.BES_TOTA_DESCRIPTOR_OTA_UUID);
                break;
            case R.id.connect_device:
//                initlayout();
                loadinganim();
                if (mHmDevice == null) {
                    loadingDialog.dismiss();
                    ActivityUtils.showToast(R.string.connect_failed);
                    return;
                }
                runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        Log.i(TAG, "run: 1");
                        mServiceConfig.setDevice(mHmDevice);
                        Log.i(TAG, "onPickDevice:1111 " + mDevice.getAddress());
                        mServiceConfig.setTotaConnect(true);
                        boolean useTotaV2 = (boolean) SPHelper.getPreference(instance, BesSdkConstants.BES_TOTA_USE_TOTAV2, BesSdkConstants.BES_TOTA_USE_TOTAV2_VALUE);
                        mServiceConfig.setUseTotaV2(useTotaV2);
                        mPresenter.connectDevice(mServiceConfig, instance, instance);
                    }
                });
                break;
            case R.id.earbuds_click_left:
                mPresenter.sendTestData(COMMAND_SET_TYPE_BUTTON_SETTINGS_CONTROL, true, BUTTON_SETTINGS_CONTROL_CLICK_LEFT, BUTTON_SETTINGS_CONTROL_CLICK, curEarbudsClickType);

                getButtonState();
                break;
            case R.id.earbuds_click_right:
                mPresenter.sendTestData(COMMAND_SET_TYPE_BUTTON_SETTINGS_CONTROL, true, BUTTON_SETTINGS_CONTROL_CLICK_RIGHT, BUTTON_SETTINGS_CONTROL_CLICK, curEarbudsClickType);

                getButtonState();
                break;
            case R.id.earbuds_double_click_left:
                mPresenter.sendTestData(COMMAND_SET_TYPE_BUTTON_SETTINGS_CONTROL, true, BUTTON_SETTINGS_CONTROL_CLICK_LEFT, BUTTON_SETTINGS_CONTROL_DOUBLE_CLICK, curEarbudsClickType);

                getButtonState();
                break;
            case R.id.earbuds_double_click_right:
                mPresenter.sendTestData(COMMAND_SET_TYPE_BUTTON_SETTINGS_CONTROL, true, BUTTON_SETTINGS_CONTROL_CLICK_RIGHT, BUTTON_SETTINGS_CONTROL_DOUBLE_CLICK, curEarbudsClickType);

                getButtonState();
                break;
            case R.id.earbuds_triple_click_left:
                mPresenter.sendTestData(COMMAND_SET_TYPE_BUTTON_SETTINGS_CONTROL, true, BUTTON_SETTINGS_CONTROL_CLICK_LEFT, BUTTON_SETTINGS_CONTROL_TRIPLE_CLICK, curEarbudsClickType);

                getButtonState();
                break;
            case R.id.earbuds_triple_click_right:
                mPresenter.sendTestData(COMMAND_SET_TYPE_BUTTON_SETTINGS_CONTROL, true, BUTTON_SETTINGS_CONTROL_CLICK_RIGHT, BUTTON_SETTINGS_CONTROL_TRIPLE_CLICK, curEarbudsClickType);

                getButtonState();
                break;
            case R.id.earbuds_long_press_left:
                mPresenter.sendTestData(COMMAND_SET_TYPE_BUTTON_SETTINGS_CONTROL, true, BUTTON_SETTINGS_CONTROL_CLICK_LEFT, BUTTON_SETTINGS_CONTROL_LONG_PRESS, curEarbudsClickType);

                getButtonState();
                break;
            case R.id.earbuds_long_press_right:
                mPresenter.sendTestData(COMMAND_SET_TYPE_BUTTON_SETTINGS_CONTROL, true, BUTTON_SETTINGS_CONTROL_CLICK_RIGHT, BUTTON_SETTINGS_CONTROL_LONG_PRESS, curEarbudsClickType);

                getButtonState();
                break;
            case R.id.factory_reset_cmd_set:
                mPresenter.sendTestData(COMMAND_SET_TYPE_FACTORY_RESET, false, (byte) 0x00);
                break;
            case R.id.play:
                mPresenter.sendTestData(COMMAND_SET_TYPE_MUSIC_PLAY_BACK, true, MUSIC_PLAY_BACK_PLAY);
                break;
            case R.id.pause:
                mPresenter.sendTestData(COMMAND_SET_TYPE_MUSIC_PLAY_BACK, true, MUSIC_PLAY_BACK_PAUSE);
                break;
            case R.id.next:
                mPresenter.sendTestData(COMMAND_SET_TYPE_MUSIC_PLAY_BACK, true, MUSIC_PLAY_BACK_NEXT);
                break;
            case R.id.prev:
                mPresenter.sendTestData(COMMAND_SET_TYPE_MUSIC_PLAY_BACK, true, MUSIC_PLAY_BACK_PREV);
                break;
            case R.id.button_get_left_battery:
                mPresenter.sendTestData(COMMAND_SET_TYPE_BATTREY_PERCENTAGE, true, BATTREY_PERCENTAGE_LEFT);
                break;
            case R.id.button_get_right_battery:
                mPresenter.sendTestData(COMMAND_SET_TYPE_BATTREY_PERCENTAGE, true, BATTREY_PERCENTAGE_RIGHT);
                break;
//            case R.id.fit_test:
//                if (switchButton_fit_test.isChecked()) {
//                    fit_test.setClickable(false);
//                    fit_test.setText("Is testing");
//                }
//                mPresenter.sendTestData(COMMAND_SET_TYPE_EARBUD_FIT_TEST, true, switchButton_fit_test.isChecked () ? (byte) 0x01 : (byte) 0x00);
//                break;
            case R.id.eq_test:
//                if (eq_text.getText().length() > 1) {
//                    byte eqData = ArrayUtil.toBytes(eq_text.getText().toString())[0];
//                    mPresenter.sendTestData(COMMAND_SET_TYPE_EQ, eqData);
//                }
                mPresenter.sendTestData(COMMAND_SET_TYPE_EQ_SWITCH, true,  SWITCH_STATUS_SET_PARAM, curEqBaseType);
                break;
            case R.id.disconnect:
                mPresenter.stopSpp();
//                onStateChangedMessage(COMMAND_SET_RECEIVE_CURRENT_PRODUCT_MODEL, COMMAND_SET_PRODUCT_MODEL_ZENITH, null);
//                onStateChangedMessage(COMMAND_SET_RECEIVE_BUTTON_SETTINGS_CONTROL, "01,01,0a,01", null);

                break;
            case R.id.start_ota_btn:
                if (curModelName.length() == 0) {
                    ActivityUtils.showToast("please check Product Model");
                    return;
                }
                if (curModelName.equals(COMMAND_SET_PRODUCT_MODEL_IE2A)) {
                    start_ota_btn.setEnabled(false);
                    mPresenter.sendTestData(COMMAND_SET_TYPE_START_OTA, false, (byte) 0x00);
                } else {
                    mPresenter.stopSpp();
                    Intent intent5 = new Intent();
                    BesServiceConfig serviceConfig5 = new BesServiceConfig();
                    serviceConfig5.setDeviceProtocol(DeviceProtocol.PROTOCOL_SPP);
                    serviceConfig5.setServiceUUID(BesSdkConstants.BES_OTA_SERVICE_OTA_UUID);
                    serviceConfig5.setUSER_FLAG(1);
                    serviceConfig5.setTotaConnect(false);
                    serviceConfig5.setUseTotaV2(false);
                    serviceConfig5.setDevice(mHmDevice);
                    intent5.putExtra(Constants.OTA_SERVICE_CONFIG, (Serializable) serviceConfig5);
                    ActivityUtils.gotoAct(intent5, instance, OtaUIActivity.class);
                }
                break;
            case R.id.get_bt_state:
                refreshBtState(mPresenter.getBtState());
                break;
            case R.id.get_spp_state:
//                startTimer();
//                mPresenter.sendTestData(COMMAND_SET_TYPE_GET_SPP_CONNECT_STATUS, false, (byte) 0x00);
                break;
            case R.id.check_left_speaker:
                refreshBtState(mPresenter.getBtState());
                if (bt_state_text.getCurrentTextColor() != getColor(R.color.green)) {
                    ActivityUtils.showToast("please check bt state");
                    return;
                }
                refreshSpeakerButtonState(false, 0);
                mPresenter.startPlayVideoWithType(instance, 0, new MediaPlayer.OnCompletionListener() {
                    @Override
                    public void onCompletion(MediaPlayer mp) {
                        Log.i(TAG, "onCompletion: -----left");
                        refreshSpeakerButtonState(true, 0);
                    }
                });
                break;
            case R.id.check_right_speaker:
                refreshBtState(mPresenter.getBtState());
                if (bt_state_text.getCurrentTextColor() != getColor(R.color.green)) {
                    ActivityUtils.showToast("please check bt state");
                    return;
                }
                refreshSpeakerButtonState(false, 1);
                mPresenter.startPlayVideoWithType(instance, 1, new MediaPlayer.OnCompletionListener() {
                    @Override
                    public void onCompletion(MediaPlayer mp) {
                        Log.i(TAG, "onCompletion: -----right");
                        refreshSpeakerButtonState(true, 1);
                    }
                });
                break;
            case R.id.check_mic_state:
                refreshBtState(mPresenter.getBtState());
                if (bt_state_text.getCurrentTextColor() != getColor(R.color.green)) {
                    ActivityUtils.showToast("please check bt state");
                    return;
                }
                refreshSpeakerButtonState(false, 2);

                mPresenter.checkMicState(instance, instance, instance);
                break;
            case R.id.dolby_type_natual:
                if (curDolbyType == 1) {
                    curDolbyType = 0;
                    mPresenter.sendTestData(COMMAND_SET_TYPE_DOLBY_SWITCH, true, SWITCH_STATUS_SET_PARAM, DOLBY_TYPE_NO_STATE);
                } else {
                    curDolbyType = 1;
                    mPresenter.sendTestData(COMMAND_SET_TYPE_DOLBY_SWITCH, true, SWITCH_STATUS_SET_PARAM, DOLBY_TYPE_NATUAL);
                }
                refreshDolbyButtonUI();
                break;
            case R.id.dolby_type_movie:
                if (curDolbyType == 2) {
                    curDolbyType = 0;
                    mPresenter.sendTestData(COMMAND_SET_TYPE_DOLBY_SWITCH, true, SWITCH_STATUS_SET_PARAM, DOLBY_TYPE_NO_STATE);
                } else {
                    curDolbyType = 2;
                    mPresenter.sendTestData(COMMAND_SET_TYPE_DOLBY_SWITCH, true, SWITCH_STATUS_SET_PARAM, DOLBY_TYPE_MOVIE);
                }
                refreshDolbyButtonUI();
                break;
            case R.id.done:
                loginfo.setVisibility(View.GONE);
                break;
            case R.id.tv_title:
                loginfo.setVisibility(View.VISIBLE);
                break;
            default:
                break;
        }
    }

    private void startTimer() {
        refreshBtState(mPresenter.getBtState());
//        runOnUiThread(new Runnable() {
//            @Override
//            public void run() {
//                mPresenter.sendTestData(COMMAND_SET_TYPE_GET_SPP_CONNECT_STATUS, false, (byte) 0x00);
//            }
//        });
//        hasGetSppStatus = false;
        if (getSppStatusTimer != null) {
            getSppStatusTimer.cancel();
            getSppStatusTimer = null;
        }
        getSppStatusTimer = new Timer();
        getSppStatusTimerTask = new TimerTask()
        {
            @Override
            public void run()
            {
//                Log.i(TAG, "getSppStatusTimer: -------" + hasGetSppStatus);
//                if (!hasGetSppStatus) {
//                    refreshSppState(0);
//                }
                getSppStatusTimer.cancel();
                getSppStatusTimer = null;
                startTimer();
            }
        };

        getSppStatusTimer.schedule(getSppStatusTimerTask, 2000);
    }


    @Override
    protected void onPause() {
        super.onPause();
        if (getSppStatusTimer != null) {
            getSppStatusTimer.cancel();
            getSppStatusTimer = null;
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        startTimer();
    }

    private void refreshBtState(int state) {
        if (bt_state_text == null) {
            return;
        }
        if (state > 0) {
            bt_state_text.setText("Connect");
            bt_state_text.setTextColor(getColor(R.color.green));
        } else {
            bt_state_text.setText("disconnect");
            bt_state_text.setTextColor(getColor(R.color.fff06e6e));
        }
    }

    private void refreshSppState(int state) {
        if (spp_state_text == null) {
            return;
        }
        if (state == 1) {
            spp_state_text.setText("Connect");
            spp_state_text.setTextColor(getColor(R.color.green));
        } else {
            spp_state_text.setText("disconnect");
            spp_state_text.setTextColor(getColor(R.color.fff06e6e));
        }
    }

    private void refreshSpeakerButtonState(boolean enable, int type) {
        check_left_speaker.setEnabled(enable);
        check_right_speaker.setEnabled(enable);
        check_mic_state.setEnabled(enable);
        if (enable) {
            check_left_speaker.setText("Check Left Speaker");
            check_right_speaker.setText("Check Right Speaker");

            check_mic_state.setText("Check MIC State");
            check_mic_state.setTextColor(getColor(R.color.white));
            check_mic_state.setEnabled(true);
        } else {
            if (type == 0) {
                check_left_speaker.setText("Playing...");
            } else if (type == 1) {
                check_right_speaker.setText("Playing...");
            } else {
                check_mic_state.setText("Please speak...");
                check_mic_state.setTextColor(getColor(R.color.ffff5d5d));

                mic_state_text.setTextColor(getColor(R.color.black));
                mic_state_text.setText("Detection progress：" + 0 + "%");
            }
        }
    }

    @Override
    public void onTotaConnectState(boolean state, HmDevice hmDevice) {
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                if (state == true) {
                    initlayout();
                    loadingDialog.dismiss();

                    getButtonState();
                    if (mServiceConfig.getDeviceProtocol() == DeviceProtocol.PROTOCOL_SPP) {
                        refreshSppState(1);
                    }
                }
            }
        });
    }

    @Override
    public void onErrorMessage(int msg, HmDevice hmDevice) {

    }

    @Override
    public void onStateChangedMessage(int msg, String msgStr, HmDevice hmDevice) {
        Log.i(TAG, "onStateChangedMessage: +" + msgStr);
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                if (msg == BES_CONNECT_ERROR) {
                    Log.i(TAG, "run: failed");
                    loadingDialog.dismiss();
                    ActivityUtils.showToast(R.string.connect_failed);
                    if (mServiceConfig.getDeviceProtocol() == DeviceProtocol.PROTOCOL_SPP) {
                        refreshSppState(0);
                    }
                } else if (msg == COMMAND_SET_RECEIVE_BUTTON_SETTINGS_CONTROL) {
                    String text = "set";
                    byte[] data = ArrayUtil.toBytes(msgStr);
                    if (data[0] == BUTTON_SETTINGS_CONTROL_CLICK_LEFT) {
                        text = text + " left ear";
                    } else {
                        text = text + " right ear";
                    }
                    if (data[3] == BUTTON_SETTINGS_CONTROL_RESULT_NO_CONNECT) {
                        text = text + ":Earphone not connected";
                    } else {
                        if (data[1] == BUTTON_SETTINGS_CONTROL_CLICK) {
                            text = text + " click";
                        } else {
                            text = text + " double click";
                        }
                        if (data[2] == BUTTON_SETTINGS_CONTROL_LAST_MUSIC) {
                            text = text + " last music";
                        } else if (data[2] == BUTTON_SETTINGS_CONTROL_NEXT_MUSIC) {
                            text = text + " next music";
                        } else if (data[2] == BUTTON_SETTINGS_CONTROL_AMBIENT_MUSIC) {
                            text = text + " ambient music";
                        } else if (data[2] == BUTTON_SETTINGS_CONTROL_PHONE_CALL_BACK) {
                            text = text + " call back";
                        } else if (data[2] == BUTTON_SETTINGS_CONTROL_VOLUME_ADD) {
                            text = text + " volume ++";
                        } else if (data[2] == BUTTON_SETTINGS_CONTROL_VOLUME_LOSE) {
                            text = text + " volume --";
                        } else if (data[2] == BUTTON_SETTINGS_CONTROL_PLAY_MUSIC) {
                            text = text + " play music";
                        } else if (data[2] == BUTTON_SETTINGS_CONTROL_STOP_MUSIC) {
                            text = text + " stop music";
                        } else if (data[2] == BUTTON_SETTINGS_CONTROL_ASSISTANT) {
                            text = text + " assistant";
                        } else if (data[2] == BUTTON_SETTINGS_CONTROL_PLAY_PAUSE_MUSIC) {
                            text = text + " play pause music";
                        } else if (data[2] == BUTTON_SETTINGS_CONTROL_GAME_MODE) {
                            text = text + " game mode";
                        } else if (data[2] == BUTTON_SETTINGS_CONTROL_ALGO) {
                            text = text + " ALGO";
                        }
                        if (data[3] == BUTTON_SETTINGS_CONTROL_RESULT_SUCCESS) {
                            text = text + ":SUCCESS";
                        } else {
                            text = text + ":FAIL";
                        }
                    }
                    ActivityUtils.showToast(text);
                } else if (msg == COMMAND_SET_RECEIVE_DATA) {
                    command_set_receive_data.setText(msgStr);
//                    addlog("---------------");
//                    addlog(msgStr);

                    FileUtils.writeTOfileAndActiveClear("CommandSet", msgStr);
                } else if (msg == COMMAND_SET_RECEIVE_BATTERY_LEFT) {
                    text_left_battery.setText(msgStr);
                } else if (msg == COMMAND_SET_RECEIVE_BATTERY_RIGHT) {
                    text_right_battery.setText(msgStr);
                } else if (msg == COMMAND_SET_RECEIVE_IS_FIT) {
                    fit_test.setClickable(true);
                    fit_test.setText("Earbud Fit Test");
                    fit_text.setText(msgStr);
                } else if (msg == COMMAND_SET_RECEIVE_IN_EAR_DETECTION_RESULT) {
                    Log.i(TAG, "run: ------msg == COMMAND_SET_RECEIVE_IN_EAR_DETECTION_RESULT");
                    byte[] data = ArrayUtil.toBytes(msgStr);
                    if (data[0] == IN_EAR_DETECTION_ALL) {
                        refreshInEarDetectionUI(true, data[1] == IN_EAR_DETECTION_CLOSE ? isClosed : (data[2] == IN_EAR_DETECTION_RESULT_OUT_EAR ? outEar : inEar));
                        refreshInEarDetectionUI(false, data[3] == IN_EAR_DETECTION_CLOSE ? isClosed : (data[4] == IN_EAR_DETECTION_RESULT_OUT_EAR ? outEar : inEar));
                    } else {
                        refreshInEarDetectionUI(data[0] == IN_EAR_DETECTION_LEFT_EAR ? true : false, data[1] == IN_EAR_DETECTION_CLOSE ? isClosed : (data[2] == IN_EAR_DETECTION_RESULT_OUT_EAR ? outEar : inEar));
                    }
                } else if (msg == COMMAND_SET_RECEIVE_SPP_CONNECT_STATUS) {
//                    hasGetSppStatus = true;
//                    refreshSppState(Integer.valueOf(msgStr));
                } else if (msg == COMMAND_SET_RECEIVE_BUTTON_STATUS) {
                    String[] curState = new String[]{"", "Last Music", "Next Music", "ANC", "Call back", "Volume+", "Volume-", "Play music", "Stop music", "Wake up voice assistant", "play/pause", "game mode", "ALGO"};
                    byte[] data = ArrayUtil.toBytes(msgStr);
                    if (data[0] == GET_BUTTON_STATE_LEFT_CLICK) {
                        button_state_1.setText(curState[data[1]]);
                    }
                    if (data[2] == GET_BUTTON_STATE_RIGHT_CLICK) {
                        button_state_2.setText(curState[data[3]]);
                    }
                    if (data[4] == GET_BUTTON_STATE_LEFT_DOUBLE_CLICK) {
                        button_state_3.setText(curState[data[5]]);
                    }
                    if (data[6] == GET_BUTTON_STATE_RIGHT_DOUBLE_CLICK) {
                        button_state_4.setText(curState[data[7]]);
                    }
                    if (data[8] == GET_BUTTON_STATE_LEFT_TRIPLE_CLICK) {
                        int s = data[9];
                        if (s > 0 && s < curState.length) {
                            button_state_5.setText(curState[s]);
                        }
                    } if (data[10] == GET_BUTTON_STATE_RIGHT_TRIPLE_CLICK) {
                        int s = data[11];
                        if (s > 0 && s < curState.length) {
                            button_state_6.setText(curState[s]);
                        }
                    } if (data[12] == GET_BUTTON_STATE_LEFT_LONG_PRESS) {
                        int s = data[13];
                        if (s > 0 && s < curState.length) {
                            button_state_7.setText(curState[s]);
                        }
                    } if (data[14] == GET_BUTTON_STATE_RIGHT_LONG_PRESS) {
                        int s = data[15];
                        if (s > 0 && s < curState.length) {
                            button_state_8.setText(curState[s]);
                        }
                    }
                } else if (msg == COMMAND_SET_RECEIVE_CURRENT_PRODUCT_MODEL) {
                    command_set_current_product_model.setText("PRODUCT MODEL:" + msgStr);
                    curModelName = msgStr;
                    if (msgStr.equals(COMMAND_SET_PRODUCT_MODEL_IE2A)) {
                        linear_bes_spatial_state.setVisibility(View.VISIBLE);
                        getSwitchState(COMMAND_SET_TYPE_BES_SPATIAL_SWITCH);
                    } else if (msgStr.equals(COMMAND_SET_PRODUCT_MODEL_IM2_DOLBY) || msgStr.equals(COMMAND_SET_PRODUCT_MODEL_IM2_DOLBY_TIANIUM) || msgStr.equals(COMMAND_SET_PRODUCT_MODEL_R1)) {
                        linear_dolby_state.setVisibility(View.VISIBLE);
                        getSwitchState(COMMAND_SET_TYPE_DOLBY_SWITCH);

                        linear_mimi_state.setVisibility(View.VISIBLE);
                        getSwitchState(COMMAND_SET_TYPE_MIMI_SWITCH);
                    } else if (msgStr.equals(COMMAND_SET_PRODUCT_MODEL_IH5)) {
                        linear_bes_spatial_state.setVisibility(View.VISIBLE);
                        getSwitchState(COMMAND_SET_TYPE_BES_SPATIAL_SWITCH);

                        linear_mimi_state.setVisibility(View.VISIBLE);
                        getSwitchState(COMMAND_SET_TYPE_MIMI_SWITCH);

                        linear_anc_state.setVisibility(View.VISIBLE);
                        getSwitchState(COMMAND_SET_TYPE_REGULATE_ANC);
                    } else if (msgStr.equals(COMMAND_SET_PRODUCT_MODEL_IH6)) {
                        linear_ceva_state.setVisibility(View.VISIBLE);
                        getSwitchState(COMMAND_SET_TYPE_CEAV_SWITCH);

                        linear_mimi_state.setVisibility(View.VISIBLE);
                        getSwitchState(COMMAND_SET_TYPE_MIMI_SWITCH);

                        linear_anc_state.setVisibility(View.VISIBLE);
                        getSwitchState(COMMAND_SET_TYPE_REGULATE_ANC);
                    } else if (msgStr.equals(COMMAND_SET_PRODUCT_MODEL_R2)) {
                        linear_bes_spatial_state.setVisibility(View.VISIBLE);
                        getSwitchState(COMMAND_SET_TYPE_BES_SPATIAL_SWITCH);

                        linear_mimi_state.setVisibility(View.VISIBLE);
                        getSwitchState(COMMAND_SET_TYPE_MIMI_SWITCH);

                        linear_anc_state.setVisibility(View.VISIBLE);
                        getSwitchState(COMMAND_SET_TYPE_REGULATE_ANC);
                    } else if (msgStr.equals(COMMAND_SET_PRODUCT_MODEL_B2)) {
                        linear_mimi_state.setVisibility(View.VISIBLE);
                        getSwitchState(COMMAND_SET_TYPE_MIMI_SWITCH);
                    } else if (msgStr.equals(COMMAND_SET_PRODUCT_MODEL_ANAVRIN) || msgStr.equals(COMMAND_SET_PRODUCT_MODEL_NEBULA) || msgStr.equals(COMMAND_SET_PRODUCT_MODEL_ZENITH)) {
                        linear_dolby_state.setVisibility(View.VISIBLE);
                        getSwitchState(COMMAND_SET_TYPE_DOLBY_SWITCH);

                        linear_mimi_state.setVisibility(View.VISIBLE);
                        getSwitchState(COMMAND_SET_TYPE_MIMI_SWITCH);

                        linear_anc_state.setVisibility(View.VISIBLE);
                        getSwitchState(COMMAND_SET_TYPE_REGULATE_ANC);
                    }
                } else if (msg == COMMAND_SET_RECEIVE_REGULATE_ANC) {
                    isReceiveButtonCmd = true;
                    byte[] ancState = ArrayUtil.toBytes(msgStr);
                    if (ancState[0] == REGULATE_ANC_TYPE_ANC) {
                        regulate_anc_type.check(R.id.regulate_anc_anc);
                    } else if (ancState[0] == REGULATE_ANC_TYPE_AMNBITNE) {
                        regulate_anc_type.check(R.id.regulate_anc_ambient);
                    } else if (ancState[0] == REGULATE_ANC_TYPE_DEFAULT) {
                        regulate_anc_type.check(R.id.regulate_anc_default);
                    }
                    isReceiveButtonCmd = false;
                } else if (msg == COMMAND_SET_RECEIVE_EQ_SWITCH) {
                    isReceiveButtonCmd = true;
                    byte[] eqState = ArrayUtil.toBytes(msgStr);
                    if (eqState[0] == SWITCH_STATUS_OPEN) {
                        eq_switch_type.check(R.id.eq_switch_open);
                    } else if (eqState[0] == SWITCH_STATUS_OFF) {
                        eq_switch_type.check(R.id.eq_switch_off);
                    }
                    isReceiveButtonCmd = false;
                    if (eqState[1] == (byte) 0x00) {
                        return;
                    }
                    isReceiveButtonCmd = true;
//                    if (eqState[1] == EQ_TYPE_DEFAULT) {
//                        eq_basetype.check(R.id.eq_basetype_default);
//                    } else
                    if (eqState[1] == EQ_TYPE_POP) {
                        eq_basetype.check(R.id.eq_basetype_pop);
                    } else if (eqState[1] == EQ_TYPE_ROCK) {
                        eq_basetype.check(R.id.eq_basetype_rock);
                    } else if (eqState[1] == EQ_TYPE_JAZZ) {
                        eq_basetype.check(R.id.eq_basetype_jazz);
                    } else if (eqState[1] == EQ_TYPE_CLASSIC) {
                        eq_basetype.check(R.id.eq_basetype_classic);
                    } else if (eqState[1] == EQ_TYPE_COUNTRY) {
                        eq_basetype.check(R.id.eq_basetype_country);
                    }
                    isReceiveButtonCmd = false;
                } else if (msg == COMMAND_SET_RECEIVE_DOLBY_SWITCH) {
                    isReceiveButtonCmd = true;
                    byte[] dolbyState = ArrayUtil.toBytes(msgStr);
                    if (dolbyState[0] == SWITCH_STATUS_OPEN) {
                        dolby_switch_type.check(R.id.dolby_switch_open);
                        dolby_switch_title.setText("Dolby State: ON");
                        dolby_switch_title.setTextColor(getColor(R.color.green));
                    } else if (dolbyState[0] == SWITCH_STATUS_OFF) {
                        dolby_switch_type.check(R.id.dolby_switch_off);
                        dolby_switch_title.setText("Dolby State: OFF");
                        dolby_switch_title.setTextColor(getColor(R.color.fff06e6e));
                    }
                    isReceiveButtonCmd = false;
                    if (dolbyState.length > 1) {
                        curDolbyType = dolbyState[1];
                        refreshDolbyButtonUI();
                    }
                } else if (msg == COMMAND_SET_RECEIVE_BES_SPATIAL_SWITCH) {
                    isReceiveButtonCmd = true;
                    byte[] state = ArrayUtil.toBytes(msgStr);
                    if (state[0] == SWITCH_STATUS_OPEN) {
                        bes_spatial_switch_type.check(R.id.bes_spatial_switch_open);
                    } else if (state[0] == SWITCH_STATUS_OFF) {
                        bes_spatial_switch_type.check(R.id.bes_spatial_switch_off);
                    }
                    isReceiveButtonCmd = false;
                } else if (msg == COMMAND_SET_RECEIVE_MIMI_SWITCH) {
                    isReceiveButtonCmd = true;
                    byte[] state = ArrayUtil.toBytes(msgStr);
                    if (state[0] == SWITCH_STATUS_OPEN) {
                        mimi_switch_type.check(R.id.mimi_switch_open);
                    } else if (state[0] == SWITCH_STATUS_OFF) {
                        mimi_switch_type.check(R.id.mimi_switch_off);
                    }

                    if (state.length > 1) {
                        if (state[1] == (byte) 1) {
                            mimi_switch_preset.check(R.id.mimi_switch_preset_18);
                        } else if (state[1] == (byte) 2) {
                            mimi_switch_preset.check(R.id.mimi_switch_preset_45);
                        } else if (state[1] == (byte) 3) {
                            mimi_switch_preset.check(R.id.mimi_switch_preset_60);
                        }
                        if (state[2] == (byte) 1) {
                            mimi_switch_intensity.check(R.id.mimi_switch_intensity_0);
                        } else if (state[2] == (byte) 2) {
                            mimi_switch_intensity.check(R.id.mimi_switch_intensity_0_5);
                        } else if (state[2] == (byte) 3) {
                            mimi_switch_intensity.check(R.id.mimi_switch_intensity_1_0);
                        }
                    }
                    isReceiveButtonCmd = false;
                } else if (msg == COMMAND_SET_RECEIVE_CEVA_SWITCH) {
                    isReceiveButtonCmd = true;
                    byte[] state = ArrayUtil.toBytes(msgStr);
                    if (state[0] == SWITCH_STATUS_OPEN) {
                        ceva_switch_type.check(R.id.ceva_switch_open);
                    } else if (state[0] == SWITCH_STATUS_OFF) {
                        ceva_switch_type.check(R.id.ceva_switch_off);
                    }
                    isReceiveButtonCmd = false;
                } else if (msg == COMMAND_SET_RECEIVE_VERSION_CRC) {
                    command_set_current_version.setText(msgStr);
                } else if (msg == COMMAND_SET_RECEIVE_START_OTA) {
                    mPresenter.stopSpp();
                    try {
                        Thread.sleep(2000);
                    } catch (InterruptedException e) {
                        throw new RuntimeException(e);
                    }
                    Intent intent0 = new Intent();
                    BesServiceConfig serviceConfig0 = new BesServiceConfig();
                    serviceConfig0.setDeviceProtocol(DeviceProtocol.PROTOCOL_SPP);
                    serviceConfig0.setServiceUUID(BesSdkConstants.BES_OTA_SERVICE_OTA_UUID_OLD);
                    serviceConfig0.setUSER_FLAG(-1);
                    serviceConfig0.setDevice(mHmDevice);
                    intent0.putExtra(Constants.OTA_SERVICE_CONFIG, (Serializable) serviceConfig0);
                    ActivityUtils.gotoAct(intent0, instance, OtaUIActivity.class);
                }
            }
        });
    }

    private void getSwitchState(byte type) {
        new Thread(new Runnable() {
            @Override
            public void run() {
                try {
                    Thread.sleep(50);
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            mPresenter.sendTestData(type, true, SWITCH_STATUS_GET);
                        }
                    });
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }
            }
        }).start();
    }

    @Override
    public void onSuccessMessage(int msg, HmDevice hmDevice) {

    }

    public void initlayout() {
        setContentView(R.layout.activity_commandset);
        inittoolbar(cur_title);

        logV = (TextView) findViewById(R.id.logV);
        done = (Button) findViewById(R.id.done);
        loginfo = (View) findViewById(R.id.loginfo);
        tv_title.setOnClickListener(instance);
        done.setOnClickListener(instance);

        earbuds_click_func_0 = (RadioGroup) findViewById(R.id.earbuds_click_func_0);
        earbuds_click_func_1 = (RadioGroup) findViewById(R.id.earbuds_click_func_1);
        earbuds_click_func_2 = (RadioGroup) findViewById(R.id.earbuds_click_func_2);
        earbuds_click_func_3 = (RadioGroup) findViewById(R.id.earbuds_click_func_3);

        earbuds_click_left = (Button) findViewById(R.id.earbuds_click_left);
        earbuds_click_right = (Button) findViewById(R.id.earbuds_click_right);
        earbuds_double_click_left = (Button) findViewById(R.id.earbuds_double_click_left);
        earbuds_double_click_right = (Button) findViewById(R.id.earbuds_double_click_right);
        earbuds_triple_click_left = (Button) findViewById(R.id.earbuds_triple_click_left);
        earbuds_triple_click_right = (Button) findViewById(R.id.earbuds_triple_click_right);
        earbuds_long_press_left = (Button) findViewById(R.id.earbuds_long_press_left);
        earbuds_long_press_right = (Button) findViewById(R.id.earbuds_long_press_right);

        factory_reset_cmd_set = (Button) findViewById(R.id.factory_reset_cmd_set);
        play = (Button) findViewById(R.id.play);
        pause = (Button) findViewById(R.id.pause);
        next = (Button) findViewById(R.id.next);
        prev = (Button) findViewById(R.id.prev);
        button_get_left_battery = (Button) findViewById(R.id.button_get_left_battery);
        text_left_battery = (TextView) findViewById(R.id.text_left_battery);
        button_get_right_battery = (Button) findViewById(R.id.button_get_right_battery);
        text_right_battery = (TextView) findViewById(R.id.text_right_battery);
//        fit_test = (Button) findViewById(R.id.fit_test);
//        switchButton_fit_test = (com.suke.widget.SwitchButton) findViewById(R.id.switchButton_fit_test);
//        fit_text = (TextView) findViewById(R.id.fit_text);
        eq_test = (Button) findViewById(R.id.eq_test);
        eq_text = (EditText) findViewById(R.id.eq_text);
        command_set_receive_data = (EditText) findViewById(R.id.command_set_receive_data);
        eq_basetype = (RadioGroup) findViewById(R.id.eq_basetype);
        command_set_current_product_model = (TextView) findViewById(R.id.command_set_current_product_model);
        command_set_current_version = (TextView) findViewById(R.id.command_set_current_version);
        regulate_anc_type = (RadioGroup) findViewById(R.id.regulate_anc_type);
        eq_switch_type = (RadioGroup) findViewById(R.id.eq_switch_type);
        dolby_switch_type = (RadioGroup) findViewById(R.id.dolby_switch_type);
        bes_spatial_switch_type = (RadioGroup) findViewById(R.id.bes_spatial_switch_type);
        mimi_switch_type = (RadioGroup) findViewById(R.id.mimi_switch_type);
        ceva_switch_type = (RadioGroup) findViewById(R.id.ceva_switch_type);
        mimi_switch_preset = (RadioGroup) findViewById(R.id.mimi_switch_preset);
        mimi_switch_intensity = (RadioGroup) findViewById(R.id.mimi_switch_intensity);

        disconnect = (Button) findViewById(R.id.disconnect);
//        scrollview_0 = (MyScrollView) findViewById(R.id.scrollview_0);
//        scrollview_0.addInterface(instance, 0);
//        scrollview_1 = (MyScrollView) findViewById(R.id.scrollview_1);
//        scrollview_1.addInterface(instance, 1);
//        scrollview_2 = (MyScrollView) findViewById(R.id.scrollview_2);
//        scrollview_2.addInterface(instance, 2);
//        scrollview_3 = (MyScrollView) findViewById(R.id.scrollview_3);
//        scrollview_3.addInterface(instance, 3);
//        scrollview_4 = (MyScrollView) findViewById(R.id.scrollview_4);
//        scrollview_4.addInterface(instance, 4);
//        scrollview_5 = (MyScrollView) findViewById(R.id.scrollview_5);
//        scrollview_5.addInterface(instance, 5);
//        scrollview_6 = (MyScrollView) findViewById(R.id.scrollview_6);
//        scrollview_6.addInterface(instance, 6);
//        scrollview_7 = (MyScrollView) findViewById(R.id.scrollview_7);
//        scrollview_7.addInterface(instance, 7);
//        scrollview_8 = (MyScrollView) findViewById(R.id.scrollview_8);
//        scrollview_8.addInterface(instance, 8);
//        scrollview_9 = (MyScrollView) findViewById(R.id.scrollview_9);
//        scrollview_9.addInterface(instance, 9);

//        switchButton_fit_test.setChecked(true);
//        switchButton_fit_test.setOnCheckedChangeListener(instance);
        earbuds_click_func_0.setOnCheckedChangeListener(instance);
        earbuds_click_func_1.setOnCheckedChangeListener(instance);
        earbuds_click_func_2.setOnCheckedChangeListener(instance);
        earbuds_click_func_3.setOnCheckedChangeListener(instance);

        factory_reset_cmd_set.setOnClickListener(instance);
        earbuds_click_left.setOnClickListener(instance);
        earbuds_click_right.setOnClickListener(instance);
        earbuds_double_click_left.setOnClickListener(instance);
        earbuds_double_click_right.setOnClickListener(instance);
        earbuds_triple_click_left.setOnClickListener(instance);
        earbuds_triple_click_right.setOnClickListener(instance);
        earbuds_long_press_left.setOnClickListener(instance);
        earbuds_long_press_right.setOnClickListener(instance);

        play.setOnClickListener(instance);
        pause.setOnClickListener(instance);
        next.setOnClickListener(instance);
        prev.setOnClickListener(instance);
        button_get_left_battery.setOnClickListener(instance);
        button_get_right_battery.setOnClickListener(instance);

//        fit_test.setOnClickListener(instance);
        eq_test.setOnClickListener(instance);
        eq_basetype.setOnCheckedChangeListener(instance);
        regulate_anc_type.setOnCheckedChangeListener(instance);
        eq_switch_type.setOnCheckedChangeListener(instance);
        dolby_switch_type.setOnCheckedChangeListener(instance);
        bes_spatial_switch_type.setOnCheckedChangeListener(instance);
        mimi_switch_type.setOnCheckedChangeListener(instance);
        ceva_switch_type.setOnCheckedChangeListener(instance);
        mimi_switch_preset.setOnCheckedChangeListener(instance);
        mimi_switch_intensity.setOnCheckedChangeListener(instance);

        disconnect.setOnClickListener(instance);

        start_ota_btn = (Button) findViewById(R.id.start_ota_btn);
        start_ota_btn.setOnClickListener(instance);
        get_bt_state = (Button) findViewById(R.id.get_bt_state);
        get_bt_state.setOnClickListener(instance);
        bt_state_text = (TextView) findViewById(R.id.bt_state_text);
        refreshBtState(mPresenter.getBtState());

        get_spp_state = (Button) findViewById(R.id.get_spp_state);
        get_spp_state.setOnClickListener(instance);
        spp_state_text = (TextView) findViewById(R.id.spp_state_text);
        mPresenter.sendTestData(COMMAND_SET_TYPE_GET_SPP_CONNECT_STATUS, false, (byte) 0x00);

        check_mic_state = (Button) findViewById(R.id.check_mic_state);
        check_mic_state.setOnClickListener(instance);
        mic_state_text = (TextView) findViewById(R.id.mic_state_text);

        check_left_speaker = (Button) findViewById(R.id.check_left_speaker);
        check_left_speaker.setOnClickListener(instance);
        check_right_speaker = (Button) findViewById(R.id.check_right_speaker);
        check_right_speaker.setOnClickListener(instance);

        switchButton_in_ear_detection_left = (SwitchButton) findViewById(R.id.switchButton_in_ear_detection_left);
        switchButton_in_ear_detection_right = (SwitchButton) findViewById(R.id.switchButton_in_ear_detection_right);
        switchButton_in_ear_detection_left.setOnCheckedChangeListener(instance);
        switchButton_in_ear_detection_right.setOnCheckedChangeListener(instance);

        text_in_ear_detection_left = (TextView) findViewById(R.id.text_in_ear_detection_left);
        text_in_ear_detection_right = (TextView) findViewById(R.id.text_in_ear_detection_right);

        mPresenter.sendTestData(COMMAND_SET_TYPE_IN_EAR_DETECTION, true, IN_EAR_DETECTION_ALL);

        button_state_1 = (TextView) findViewById(R.id.button_state_1);
        button_state_2 = (TextView) findViewById(R.id.button_state_2);
        button_state_3 = (TextView) findViewById(R.id.button_state_3);
        button_state_4 = (TextView) findViewById(R.id.button_state_4);
        button_state_5 = (TextView) findViewById(R.id.button_state_5);
        button_state_6 = (TextView) findViewById(R.id.button_state_6);
        button_state_7 = (TextView) findViewById(R.id.button_state_7);
        button_state_8 = (TextView) findViewById(R.id.button_state_8);

        dolby_switch_title = (TextView) findViewById(R.id.dolby_switch_title);
        dolby_type_natual = (Button) findViewById(R.id.dolby_type_natual);
        dolby_type_natual.setOnClickListener(instance);
        dolby_type_movie = (Button) findViewById(R.id.dolby_type_movie);
        dolby_type_movie.setOnClickListener(instance);

        linear_dolby_state = (LinearLayout) findViewById(R.id.linear_dolby_state);
        linear_bes_spatial_state = (LinearLayout) findViewById(R.id.linear_bes_spatial_state);
        linear_mimi_state = (LinearLayout) findViewById(R.id.linear_mimi_state);
        linear_ceva_state = (LinearLayout) findViewById(R.id.linear_ceva_state);
        linear_anc_state = (LinearLayout) findViewById(R.id.linear_anc_state);

        getButtonState();

        new Thread(new Runnable() {
            @Override
            public void run() {
                try {
                    Thread.sleep(50);
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            mPresenter.sendGetVersionCrcData();
                        }
                    });
                    Thread.sleep(50);
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            mPresenter.sendTestData(COMMAND_SET_TYPE_GET_BUTTON_STATE, false, (byte)0x00);
                        }
                    });
                    Thread.sleep(50);
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            mPresenter.sendTestData(COMMAND_SET_TYPE_EQ_SWITCH, true, SWITCH_STATUS_GET);
                        }
                    });
                    Thread.sleep(50);
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            mPresenter.sendTestData(COMMAND_SET_TYPE_GET_CURRENT_PRODUCT_MODEL, false, (byte)0x00);
                        }
                    });
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }
            }
        }).start();
    }

    private void getButtonState() {
        try {
            Thread.sleep(100);
            mPresenter.sendTestData(COMMAND_SET_TYPE_GET_BUTTON_STATE, false, (byte)0x00);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
//        mPresenter.stopSpp();
    }

    @Override
    public void scrollViewDidScroll(int scrollY, int index) {

    }

    @Override
    public void onCheckedChanged(RadioGroup group, int checkedId) {
        Log.i(TAG, "onCheckedChanged: --------aaaaa");
        if (isReceiveButtonCmd) {
            return;
        }
        Log.i(TAG, "onCheckedChanged: --------" + checkedId);
        if (group == earbuds_click_func_0 || group == earbuds_click_func_1 || group == earbuds_click_func_2 || group == earbuds_click_func_3) {
            earbuds_click_func_0.check(checkedId);
            earbuds_click_func_1.check(checkedId);
            earbuds_click_func_2.check(checkedId);
            earbuds_click_func_3.check(checkedId);
        }
        switch (checkedId) {
//            case R.id.eq_basetype_default:
//                curEqBaseType = EQ_TYPE_DEFAULT;
//                break;
            case R.id.eq_basetype_pop:
                curEqBaseType = EQ_TYPE_POP;
                break;
            case R.id.eq_basetype_rock:
                curEqBaseType = EQ_TYPE_ROCK;
                break;
            case R.id.eq_basetype_jazz:
                curEqBaseType = EQ_TYPE_JAZZ;
                break;
            case R.id.eq_basetype_classic:
                curEqBaseType = EQ_TYPE_CLASSIC;
                break;
            case R.id.eq_basetype_country:
                curEqBaseType = EQ_TYPE_COUNTRY;
                break;
            case R.id.earbuds_click_func_last_music:
                curEarbudsClickType = BUTTON_SETTINGS_CONTROL_LAST_MUSIC;
                break;
            case R.id.earbuds_click_func_next_music:
                curEarbudsClickType = BUTTON_SETTINGS_CONTROL_NEXT_MUSIC;
                break;
            case R.id.earbuds_click_func_ambient_music:
                curEarbudsClickType = BUTTON_SETTINGS_CONTROL_AMBIENT_MUSIC;
                break;
            case R.id.earbuds_click_func_volume_add:
                curEarbudsClickType = BUTTON_SETTINGS_CONTROL_VOLUME_ADD;
                break;
            case R.id.earbuds_click_func_volume_lose:
                curEarbudsClickType = BUTTON_SETTINGS_CONTROL_VOLUME_LOSE;
                break;
            case R.id.earbuds_click_func_call_back:
                curEarbudsClickType = BUTTON_SETTINGS_CONTROL_PHONE_CALL_BACK;
                break;
            case R.id.earbuds_click_func_stop_music:
                curEarbudsClickType = BUTTON_SETTINGS_CONTROL_STOP_MUSIC;
                break;
            case R.id.earbuds_click_func_play_music:
                curEarbudsClickType = BUTTON_SETTINGS_CONTROL_PLAY_MUSIC;
                break;
            case R.id.earbuds_click_func_assistant:
                curEarbudsClickType = BUTTON_SETTINGS_CONTROL_ASSISTANT;
                break;
            case R.id.earbuds_click_func_play_pause_music:
                curEarbudsClickType = BUTTON_SETTINGS_CONTROL_PLAY_PAUSE_MUSIC;
                break;
            case R.id.earbuds_click_func_game_mode:
                curEarbudsClickType = BUTTON_SETTINGS_CONTROL_GAME_MODE;
                break;
            case R.id.earbuds_click_func_algo:
                curEarbudsClickType = BUTTON_SETTINGS_CONTROL_ALGO;
                break;
            case R.id.regulate_anc_anc:
                Log.i(TAG, "onCheckedChanged: --------regulate_anc_anc");

                mPresenter.sendTestData(COMMAND_SET_TYPE_REGULATE_ANC, true, SWITCH_STATUS_SET, REGULATE_ANC_TYPE_ANC);

                break;
            case R.id.regulate_anc_ambient:
                Log.i(TAG, "onCheckedChanged: --------regulate_anc_ambient");
                mPresenter.sendTestData(COMMAND_SET_TYPE_REGULATE_ANC, true, SWITCH_STATUS_SET, REGULATE_ANC_TYPE_AMNBITNE);

                break;
            case R.id.regulate_anc_default:
                Log.i(TAG, "onCheckedChanged: --------regulate_anc_default");

                mPresenter.sendTestData(COMMAND_SET_TYPE_REGULATE_ANC, true, SWITCH_STATUS_SET, REGULATE_ANC_TYPE_DEFAULT);

                break;
            case R.id.eq_switch_open:
                mPresenter.sendTestData(COMMAND_SET_TYPE_EQ_SWITCH, true, SWITCH_STATUS_SET, SWITCH_STATUS_OPEN);
                break;
            case R.id.eq_switch_off:
                mPresenter.sendTestData(COMMAND_SET_TYPE_EQ_SWITCH, true, SWITCH_STATUS_SET, SWITCH_STATUS_OFF);
                break;
            case R.id.dolby_switch_open:
                mPresenter.sendTestData(COMMAND_SET_TYPE_DOLBY_SWITCH, true, SWITCH_STATUS_SET, SWITCH_STATUS_OPEN);
                break;
            case R.id.dolby_switch_off:
                mPresenter.sendTestData(COMMAND_SET_TYPE_DOLBY_SWITCH, true, SWITCH_STATUS_SET, SWITCH_STATUS_OFF);
                break;
            case R.id.bes_spatial_switch_open:
                mPresenter.sendTestData(COMMAND_SET_TYPE_BES_SPATIAL_SWITCH, true, SWITCH_STATUS_SET, SWITCH_STATUS_OPEN);
                break;
            case R.id.bes_spatial_switch_off:
                mPresenter.sendTestData(COMMAND_SET_TYPE_BES_SPATIAL_SWITCH, true, SWITCH_STATUS_SET, SWITCH_STATUS_OFF);
                break;
            case R.id.mimi_switch_open:
                mPresenter.sendTestData(COMMAND_SET_TYPE_MIMI_SWITCH, true, SWITCH_STATUS_SET, SWITCH_STATUS_OPEN);
                break;
            case R.id.mimi_switch_off:
                mPresenter.sendTestData(COMMAND_SET_TYPE_MIMI_SWITCH, true, SWITCH_STATUS_SET, SWITCH_STATUS_OFF);
                break;
            case R.id.mimi_switch_preset_18:
                mPresenter.sendTestData(COMMAND_SET_TYPE_MIMI_SWITCH, true, SWITCH_STATUS_SET_PARAM, (byte)1);
                break;
            case R.id.mimi_switch_preset_45:
                mPresenter.sendTestData(COMMAND_SET_TYPE_MIMI_SWITCH, true, SWITCH_STATUS_SET_PARAM, (byte)2);
                break;
            case R.id.mimi_switch_preset_60:
                mPresenter.sendTestData(COMMAND_SET_TYPE_MIMI_SWITCH, true, SWITCH_STATUS_SET_PARAM, (byte)3);
                break;
            case R.id.mimi_switch_intensity_0:
                mPresenter.sendTestData(COMMAND_SET_TYPE_MIMI_SWITCH, true, SWITCH_STATUS_SET_PARAM_2, (byte)1);
                break;
            case R.id.mimi_switch_intensity_0_5:
                mPresenter.sendTestData(COMMAND_SET_TYPE_MIMI_SWITCH, true, SWITCH_STATUS_SET_PARAM_2, (byte)2);
                break;
            case R.id.mimi_switch_intensity_1_0:
                mPresenter.sendTestData(COMMAND_SET_TYPE_MIMI_SWITCH, true, SWITCH_STATUS_SET_PARAM_2, (byte)3);
                break;
            case R.id.ceva_switch_open:
                mPresenter.sendTestData(COMMAND_SET_TYPE_CEAV_SWITCH, true, SWITCH_STATUS_SET, SWITCH_STATUS_OPEN);
                break;
            case R.id.ceva_switch_off:
                mPresenter.sendTestData(COMMAND_SET_TYPE_CEAV_SWITCH, true, SWITCH_STATUS_SET, SWITCH_STATUS_OFF);
                break;
            default:
                break;

        }
    }

    private String beCloseing = "Be closing...";
    private String beOpening = "Be opening...";
    private String inEar = "Be in the ear";
    private String outEar = "Not in the ear";
    private String isClosed = "In-ear testing is turned off";
    @Override
    public void onCheckedChanged(SwitchButton view, boolean isChecked) {
        if (view == switchButton_fit_test) {
            if (isChecked) {
                fit_test.setClickable(true);
                fit_test.setText("Earbud Fit Test");

            } else {
                fit_test.setClickable(true);
                fit_test.setText("Close Fit Test");
            }
        } else if (view == switchButton_in_ear_detection_left) {
            refreshInEarDetectionUI(true, isChecked ? beOpening : beCloseing);
            mPresenter.sendTestData(COMMAND_SET_TYPE_IN_EAR_DETECTION, true, IN_EAR_DETECTION_LEFT_EAR, isChecked ? IN_EAR_DETECTION_OPEN : IN_EAR_DETECTION_CLOSE);
        } else if (view == switchButton_in_ear_detection_right) {
            refreshInEarDetectionUI(false, isChecked ? beOpening : beCloseing);
            mPresenter.sendTestData(COMMAND_SET_TYPE_IN_EAR_DETECTION, true, IN_EAR_DETECTION_RIGHT_EAR, isChecked ? IN_EAR_DETECTION_OPEN : IN_EAR_DETECTION_CLOSE);
        }
    }

    private void refreshInEarDetectionUI(boolean isLeft, String text) {
        Log.i(TAG, "refreshInEarDetectionUI: ------" + isLeft + "----" + text);
        TextView textView = isLeft ? text_in_ear_detection_left : text_in_ear_detection_right;
        SwitchButton switchButton = isLeft ? switchButton_in_ear_detection_left : switchButton_in_ear_detection_right;
        textView.setText(text);
        textView.setTextColor(getColor(R.color.activityTextMainClor));
        if (text.equals(inEar)) {
            switchButton.setChecked(true);
            textView.setTextColor(getColor(R.color.green));
        } else if (text.equals(outEar)) {
            switchButton.setChecked(true);
            textView.setTextColor(getColor(R.color.fff06e6e));
        } else if (text.equals(beOpening) || text.equals(beCloseing)) {
            textView.setTextColor(getColor(R.color.black));
        }
    }

    private void refreshDolbyButtonUI() {
        addlog("refreshDolbyButtonUI----" + curDolbyType);
        dolby_type_natual.setBackground(getDrawable(R.drawable.ota_button_bg_press));
        dolby_type_movie.setBackground(getDrawable(R.drawable.ota_button_bg_press));
        if (curDolbyType == 0) {
        } else if (curDolbyType == 1) {
            dolby_type_natual.setBackground(getDrawable(R.drawable.ota_click));
        } else if (curDolbyType == 2) {
            dolby_type_movie.setBackground(getDrawable(R.drawable.ota_click));
        }
    }

    @Override
    public void onMicStateChanged(int state, String msg) {
        Log.i(TAG, "onMicStateChanged: --------" + state + "----" + msg);
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                if (state == 0) {
                    refreshSpeakerButtonState(true, 2);

                    mic_state_text.setText("mic state normal");
                    mic_state_text.setTextColor(getColor(R.color.green));
                } else if (state == 1) {
                    mic_state_text.setTextColor(getColor(R.color.black));
                    mic_state_text.setText("Detection progress：" + msg + "%");
                } else {
                    ActivityUtils.showToast(msg);

                    refreshSpeakerButtonState(true, 2);

                    mic_state_text.setText("mic state abnormality");
                    mic_state_text.setTextColor(getColor(R.color.fff06e6e));
                }
            }
        });
    }

}



