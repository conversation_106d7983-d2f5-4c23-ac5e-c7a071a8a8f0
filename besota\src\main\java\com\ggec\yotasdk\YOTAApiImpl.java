package com.ggec.yotasdk;

import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothDevice;
import android.bluetooth.BluetoothManager;
import android.bluetooth.BluetoothProfile;
import android.content.Context;
import android.os.Environment;
import android.util.Log;

import com.bes.bessdk.BesSdkConstants;
import com.bes.bessdk.service.BesOtaService;
import com.bes.bessdk.service.base.BesServiceConfig;
import com.bes.bessdk.service.base.BesServiceListener;
import com.bes.sdk.device.HmDevice;
import com.bes.sdk.ota.OTADfuInfo;
import com.bes.sdk.ota.OTATask;
import com.bes.sdk.ota.RemoteOTAConfig;
import com.bes.sdk.utils.DeviceProtocol;
import com.bes.sdk.utils.OTAStatus;

import static com.bes.bessdk.service.BesOTAConstants.OTA_CMD_GET_HW_INFO;
import static com.bes.sdk.utils.OTAStatus.*;

import java.io.File;
import java.util.ArrayList;
import java.util.Set;

/**
 * YOTA管理器
 * 实现OTA升级功能的核心类
 */
public class YOTAApiImpl implements YOTAApi, BesServiceListener, OTATask.StatusListener {

    private static final String TAG = "YOTAApiImpl";
    private static YOTAApiImpl instance;
    
    private Context context;
    private HmDevice currentDevice;
    private String upgradeFilePath;
    private OTATask otaTask;
    private BesServiceConfig serviceConfig;
    private boolean isUpgrading = false;
    
    // 默认OTA升级文件路径
    private static final String DEFAULT_OTA_FILE_NAME = "test.bin";
    private String defaultOtaFilePath;
    
    // 监听器
    private YOTAApi.ProgressListener progressListener;
    private YOTAApi.StatusListener statusListener;
    
    private YOTAApiImpl(Context context) {
        this.context = context.getApplicationContext();
        initDefaultOtaFilePath();
    }
    
    /**
     * 初始化默认OTA升级文件路径
     */
    private void initDefaultOtaFilePath() {
        String packageName = context.getPackageName();
        defaultOtaFilePath = "/storage/emulated/0/Android/data/" + packageName + "/" + DEFAULT_OTA_FILE_NAME;
        Log.i(TAG, "默认OTA文件路径: " + defaultOtaFilePath);
    }
    
    /**
     * 获取YOTAApiImpl单例
     * 
     * @param context 应用上下文
     * @return YOTAApiImpl实例
     */
    public static synchronized YOTAApiImpl getInstance(Context context) {
        if (instance == null) {
            instance = new YOTAApiImpl(context);
        }
        return instance;
    }
    
    /**
     * 设置OTA升级文件路径（内部使用）
     * 
     * @param filePath OTA升级文件路径
     */
    void setOtaFilePath(String filePath) {
        if (filePath != null && !filePath.isEmpty()) {
            this.upgradeFilePath = filePath;
        }
    }
    
    /**
     * 获取已连接的SPP设备列表
     * 检查已配对设备是否真正处于连接状态
     * 
     * @return 已连接的SPP设备列表
     */
    public ArrayList<HmDevice> getConnectedSppDevices() {
        ArrayList<HmDevice> resultDevices = new ArrayList<>();
        
        try {
            Log.i(TAG, "获取已连接的SPP设备");
            BluetoothAdapter bluetoothAdapter = BluetoothAdapter.getDefaultAdapter();
            if (bluetoothAdapter == null) {
                Log.e(TAG, "蓝牙适配器未启用");
                return resultDevices;
            }
            
            // 获取BluetoothManager
            BluetoothManager bluetoothManager = (BluetoothManager) context.getSystemService(Context.BLUETOOTH_SERVICE);
            if (bluetoothManager == null) {
                Log.e(TAG, "无法获取BluetoothManager");
                return resultDevices;
            }
            
            // 获取已配对设备
            Set<BluetoothDevice> pairedDevices = bluetoothAdapter.getBondedDevices();
            if (pairedDevices.isEmpty()) {
                Log.i(TAG, "没有配对设备");
                return resultDevices;
            }
            
            Log.i(TAG, "找到" + pairedDevices.size() + "个配对设备，检查连接状态");
            
            // 遍历配对设备，检查是否真正连接
            for (BluetoothDevice device : pairedDevices) {
                try {
                    if (device.getType() == BluetoothDevice.DEVICE_TYPE_CLASSIC || 
                        device.getType() == BluetoothDevice.DEVICE_TYPE_DUAL) {
                        
                        // 检查设备连接状态
                        int connectionState = bluetoothManager.getConnectionState(device, BluetoothProfile.GATT);
                        boolean isConnected = (connectionState == BluetoothProfile.STATE_CONNECTED);
                        
                        // 额外检查SPP连接状态
                        boolean isSppConnected = checkSppConnectionState(device);
                        
                        if (isConnected || isSppConnected) {
                            // 只添加已连接的设备
                            HmDevice hmDevice = new HmDevice();
                            hmDevice.setDeviceMAC(device.getAddress());
                            hmDevice.setDeviceName(device.getName() != null ? device.getName() : "未知设备");
                            hmDevice.setPreferredProtocol(DeviceProtocol.PROTOCOL_SPP);
                            hmDevice.setRssi(100); // 默认信号强度
                            
                            Log.i(TAG, "添加已连接设备 " + hmDevice.getDeviceName() + "(" + hmDevice.getDeviceMAC() + ")");
                            resultDevices.add(hmDevice);
                        } else {
                            Log.i(TAG, "跳过未连接设备 " + device.getName() + "(" + device.getAddress() + ")");
                        }
                    }
                } catch (Exception e) {
                    Log.e(TAG, "检查设备连接状态异常: " + e.getMessage());
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "获取已连接设备异常: " + e.getMessage());
        }
        
        // 输出最终结果
        Log.i(TAG, "已找到的连接中SPP设备数量: " + resultDevices.size());
        logConnectedDevices(resultDevices);
        
        return resultDevices;
    }
    
    /**
     * 检查SPP连接状态
     * 因为SPP是经典蓝牙协议，需要特殊处理
     * 
     * @param device 蓝牙设备
     * @return 是否连接
     */
    private boolean checkSppConnectionState(BluetoothDevice device) {
        try {
            // 使用反射获取连接状态
            // 注意：这是一个实现细节，可能在不同Android版本中有所不同
            Class<?> bluetoothDeviceClass = BluetoothDevice.class;
            java.lang.reflect.Method isConnectedMethod = bluetoothDeviceClass.getDeclaredMethod("isConnected");
            isConnectedMethod.setAccessible(true);
            boolean isConnected = (boolean) isConnectedMethod.invoke(device);
            
            Log.i(TAG, "SPP设备 " + device.getName() + " 连接状态: " + isConnected);
            return isConnected;
        } catch (Exception e) {
            Log.e(TAG, "检查SPP连接状态失败: " + e.getMessage());
            // 反射失败时，尝试其他方法确认连接
            return false;
        }
    }
    
    /**
     * 记录已连接设备的信息
     */
    private void logConnectedDevices(ArrayList<HmDevice> devices) {
        if (devices.isEmpty()) {
            Log.i(TAG, "没有已连接的设备");
            return;
        }
        
        for (int i = 0; i < devices.size(); i++) {
            HmDevice device = devices.get(i);
            Log.i(TAG, "设备" + (i+1) + ": " + device.getDeviceName() + " (" + device.getDeviceMAC() + ")");
        }
    }
    
    @Override
    public boolean startUpgrade(HmDevice device, String filePath) {
        if (isUpgrading) {
            Log.e(TAG, "OTA升级已在进行中，请等待当前升级完成");
            return false;
        }
        
        // 获取已连接的SPP设备
        ArrayList<HmDevice> connectedDevices = getConnectedSppDevices();
        if (connectedDevices.isEmpty()) {
            Log.e(TAG, "没有已连接的SPP设备，无法开始OTA升级");
            return false;
        }
        
        // 使用第一个已连接的设备或指定设备
        if (device != null) {
            // 检查指定的设备是否在已连接设备列表中
            boolean deviceFound = false;
            for (HmDevice connectedDevice : connectedDevices) {
                if (connectedDevice.getDeviceMAC().equals(device.getDeviceMAC())) {
                    currentDevice = device;
                    deviceFound = true;
                    break;
                }
            }
            
            if (!deviceFound) {
                Log.e(TAG, "指定的设备未连接，使用第一个已连接的设备");
                currentDevice = connectedDevices.get(0);
            }
        } else {
            currentDevice = connectedDevices.get(0);
        }
        
        Log.i(TAG, "使用设备进行OTA升级: " + currentDevice.getDeviceName() + " (" + currentDevice.getDeviceMAC() + ")");
        
        // 使用提供的文件路径或默认路径
        upgradeFilePath = (filePath != null && !filePath.isEmpty()) ? filePath : defaultOtaFilePath;
        Log.i(TAG, "使用OTA文件路径: " + upgradeFilePath);
        
        // 检查文件是否存在
        File otaFile = new File(upgradeFilePath);
        if (!otaFile.exists()) {
            Log.e(TAG, "OTA文件不存在: " + upgradeFilePath);
            // 为了测试，即使文件不存在也尝试继续
            Log.w(TAG, "警告: OTA文件不存在，但仍尝试继续升级流程");
        }
        
        // 初始化ServiceConfig
        serviceConfig = new BesServiceConfig();
        serviceConfig.setDeviceProtocol(DeviceProtocol.PROTOCOL_SPP);
        serviceConfig.setServiceUUID(BesSdkConstants.BES_OTA_SERVICE_OTA_UUID);
        serviceConfig.setUseTotaV2(false);
        serviceConfig.setTotaConnect(false);
        serviceConfig.setUSER_FLAG(1);
        serviceConfig.setCurUser(1);
        serviceConfig.setDevice(currentDevice);
        
        Log.i(TAG, "创建OTA服务");
        // 创建OTA服务
        try {
            BesOtaService besOtaService = new BesOtaService(serviceConfig, this, context);
            otaTask = besOtaService;
            isUpgrading = true;
            Log.i(TAG, "OTA升级服务创建成功");
            return true;
        } catch (Exception e) {
            Log.e(TAG, "创建OTA服务失败: " + e.getMessage());
            return false;
        }
    }
    
    @Override
    public void setProgressListener(YOTAApi.ProgressListener listener) {
        this.progressListener = listener;
        Log.i(TAG, "设置进度监听器");
    }
    
    @Override
    public void setStatusListener(YOTAApi.StatusListener listener) {
        this.statusListener = listener;
        Log.i(TAG, "设置状态监听器");
    }
    
    @Override
    public boolean cancelUpgrade() {
        if (otaTask != null && isUpgrading) {
            Log.i(TAG, "取消OTA升级");
            otaTask.stopDataTransfer();
            isUpgrading = false;
            return true;
        }
        Log.i(TAG, "无法取消OTA升级，没有正在进行的升级任务");
        return false;
    }
    
    // BesServiceListener接口实现
    @Override
    public void onTotaConnectState(boolean state, HmDevice hmDevice) {
        Log.i(TAG, "TOTA连接状态变更: " + (state ? "已连接" : "已断开"));
        if (statusListener != null) {
            statusListener.onStatusChanged(state ? STATUS_STARTED : STATUS_CANCELED);
        }
    }
    
    @Override
    public void onErrorMessage(int msg, HmDevice hmDevice) {
        Log.e(TAG, "收到错误消息，错误码: " + msg);
        if (statusListener != null) {
            statusListener.onStatusChanged(STATUS_FAILED);
        }
        isUpgrading = false;
    }
    
    @Override
    public void onStateChangedMessage(int msg, String msgStr, HmDevice hmDevice) {
        Log.i(TAG, "状态变更消息: " + msg + ", " + msgStr);
        switch (msg) {
            case OTA_CMD_GET_HW_INFO:
                Log.i(TAG, "收到硬件信息，准备开始OTA升级");
                if (otaTask != null && upgradeFilePath != null) {
                    // 设置OTA配置
                    RemoteOTAConfig config = new RemoteOTAConfig();
                    config.setLocalPath(upgradeFilePath);
                    otaTask.setOtaConfig(config);
                    
                    // 开始数据传输
                    OTADfuInfo otaDfuInfo = new OTADfuInfo("001", 0); // breakpoint为0则从0开始升级，为1则从断点处升级
                    Log.i(TAG, "开始OTA数据传输");
                    otaTask.startDataTransfer(otaDfuInfo, this);
                    
                    if (statusListener != null) {
                        statusListener.onStatusChanged(STATUS_STARTED);
                    }
                } else {
                    Log.e(TAG, "OTA任务或文件路径为空，无法开始数据传输");
                }
                break;
            default:
                Log.i(TAG, "收到其他状态变更消息: " + msg);
                break;
        }
    }
    
    @Override
    public void onSuccessMessage(int msg, HmDevice hmDevice) {
        Log.i(TAG, "收到成功消息: " + msg);
        if (statusListener != null) {
            statusListener.onStatusChanged(STATUS_SUCCEED);
        }
        isUpgrading = false;
    }
    
    // OTATask.StatusListener接口实现
    @Override
    public void onOTAStatusChanged(OTAStatus newStatus, HmDevice hmDevice) {
        Log.i(TAG, "OTA状态变更: " + newStatus.getName());
        if (statusListener != null) {
            statusListener.onStatusChanged(newStatus);
        }
        
        if (newStatus == STATUS_SUCCEED || 
            newStatus == STATUS_FAILED || 
            newStatus == STATUS_CANCELED) {
            Log.i(TAG, "OTA升级结束，状态: " + newStatus.getName());
            isUpgrading = false;
        }
    }
    
    @Override
    public void onOTAProgressChanged(float progress, HmDevice hmDevice) {
        Log.i(TAG, "OTA进度更新: " + progress + "%");
        if (progressListener != null) {
            progressListener.onProgressChanged(progress);
        }
    }
}
