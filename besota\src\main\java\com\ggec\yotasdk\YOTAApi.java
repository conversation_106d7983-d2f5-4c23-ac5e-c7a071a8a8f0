package com.ggec.yotasdk;

import android.bluetooth.BluetoothDevice;

import com.bes.sdk.device.HmDevice;
import com.bes.sdk.utils.OTAStatus;

/**
 * YOTA API接口定义
 * 对外提供OTA升级相关功能
 */
public interface YOTAApi {
    
    /**
     * 开始升级
     * 
     * @param device 蓝牙设备
     * @param filePath 升级文件路径
     * @return 是否成功启动升级
     */
    boolean startUpgrade(HmDevice device, String filePath);
    
    /**
     * 注册升级进度监听器
     * 
     * @param listener 进度监听器
     */
    void setProgressListener(ProgressListener listener);
    
    /**
     * 注册状态回调监听器
     * 
     * @param listener 状态监听器
     */
    void setStatusListener(StatusListener listener);
    
    /**
     * 取消升级
     * 
     * @return 是否成功取消
     */
    boolean cancelUpgrade();
    
    /**
     * 进度监听器接口
     */
    interface ProgressListener {
        /**
         * 当进度更新时回调
         * 
         * @param progress 升级进度 (0-100)
         */
        void onProgressChanged(float progress);
    }
    
    /**
     * 状态监听器接口
     */
    interface StatusListener {
        /**
         * 当状态改变时回调
         * 
         * @param status 当前OTA状态
         */
        void onStatusChanged(OTAStatus status);
        
        /**
         * 当发生错误时回调
         * 
         * @param errorCode 错误码
         * @param message 错误信息
         */
        void onError(int errorCode, String message);
        
        /**
         * 当升级成功完成时回调
         */
        void onSuccess();
    }
}
