<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@mipmap/bes_bg6"
    >

    <include
            android:id="@+id/tool"
            layout="@layout/toolbar"
            />


    <TextView
        android:id="@+id/cur_dial_size"
        android:layout_width="match_parent"
        android:layout_height="17dp"
        android:layout_marginTop="40dp"
        android:textSize="16sp"
        android:textColor="@color/ff666666"
        android:textAlignment="center"/>

    <include
        layout="@layout/dial_show_home_bgview"
        android:layout_width="210dp"
        android:layout_height="250dp"
        android:layout_gravity="center"
        android:layout_marginTop="5dp"
        android:scaleType="fitStart"
        />

    <TextView
        android:id="@+id/photo_resolution"
        android:layout_width="match_parent"
        android:layout_height="15dp"
        android:textSize="16sp"
        android:textColor="@color/ff666666"
        android:textAlignment="center"/>
    <Button
        android:id="@+id/dial_button"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:background="@drawable/ota_button_bg_press"
        android:text="@string/current_dial"
        android:textAllCaps="false"
        android:textColor="@color/white"
        android:enabled="false"
        />



    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:background="@color/white">
        <ListView
            android:id="@+id/list_view"
            android:layout_width="150dp"
            android:layout_height="match_parent"
            android:layout_marginTop="-90dp"
            android:layout_marginLeft="140dp"
            android:scaleType="fitStart"
            android:rotation="270"
            android:divider="@color/white"
            >


        </ListView>

        <View
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginBottom="100dp"/>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="70dp"
            android:layout_marginTop="-200dp"
            android:orientation="horizontal"
            android:weightSum="9">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_weight="3"
                android:layout_height="match_parent"
                android:orientation="vertical"
                android:gravity="center">
                <ImageView
                    android:id="@+id/dial_tab_background_image"
                    android:layout_width="28dp"
                    android:layout_height="28dp"
                    android:background="@drawable/dial_background"/>

                <TextView
                    android:id="@+id/dial_tab_background_text"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="5dp"
                    android:textSize="13sp"
                    android:textAlignment="center"
                    android:text="@string/dail_background"/>

            </LinearLayout>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_weight="3"
                android:layout_height="match_parent"
                android:orientation="vertical"
                android:gravity="center">
                <ImageView
                    android:id="@+id/dial_tab_style_image"
                    android:layout_width="28dp"
                    android:layout_height="28dp"
                    android:background="@drawable/dail_style"/>

                <TextView
                    android:id="@+id/dial_tab_style_text"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="5dp"
                    android:textSize="13sp"
                    android:textAlignment="center"
                    android:text="@string/dail_style"/>

            </LinearLayout>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_weight="3"
                android:layout_height="match_parent"
                android:orientation="vertical"
                android:gravity="center">
                <ImageView
                    android:id="@+id/dial_tab_color_image"
                    android:layout_width="28dp"
                    android:layout_height="28dp"
                    android:background="@drawable/dial_color"/>

                <TextView
                    android:id="@+id/dial_tab_color_text"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="5dp"
                    android:textSize="13sp"
                    android:textAlignment="center"
                    android:text="@string/dail_color"/>

            </LinearLayout>


            <View
                android:layout_width="0dp"
                android:layout_weight="1"
                android:layout_height="match_parent"/>

        </LinearLayout>




    </LinearLayout>




</LinearLayout>