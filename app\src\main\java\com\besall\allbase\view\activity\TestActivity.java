package com.besall.allbase.view.activity;

import static com.besall.allbase.bluetooth.BluetoothConstants.Scan.BES_SCAN_RESULT;

import android.annotation.SuppressLint;
import android.bluetooth.BluetoothDevice;
import android.content.Intent;
import android.util.Log;
import android.view.View;
import android.widget.Button;

import com.bes.bessdk.BesSdkConstants;
import com.bes.bessdk.scan.BtHeleper;
import com.bes.bessdk.service.BesOtaService;
import com.bes.bessdk.service.base.BesServiceConfig;
import com.bes.bessdk.service.base.BesServiceListener;
import com.bes.bessdk.utils.SPHelper;
import com.bes.sdk.device.HmDevice;
import com.bes.sdk.ota.OTADfuInfo;
import com.bes.sdk.ota.OTATask;
import com.bes.sdk.ota.RemoteOTAConfig;
import com.bes.sdk.utils.DeviceProtocol;
import com.bes.sdk.utils.OTAStatus;
import com.besall.allbase.R;
import com.besall.allbase.bluetooth.BluetoothConstants;
import com.besall.allbase.bluetooth.scan.ScanActivity;

import com.besall.allbase.common.utils.ActivityUtils;
import com.besall.allbase.common.utils.FileUtils;
import com.besall.allbase.view.base.BaseActivity;

public class TestActivity extends BaseActivity<ITestActivity, TestPresenter> implements ITestActivity, View.OnClickListener, BesServiceListener, OTATask.StatusListener {

    private static TestActivity instance;

    BluetoothDevice mDevice;
    private Button testBtn;
    private Button testBtn2;
    private Button testBtn3;
    private Button testBtn4;

    BesOtaService besOtaService;
    OTATask otaTask;

    @Override
    protected TestPresenter createPresenter() {
        return new TestPresenter();
    }

    @Override
    protected void initBeforeSetContent() {

    }

    @Override
    protected int getContentViewId() {
        return R.layout.act_test;
    }

    @Override
    protected void bindView() {
        testBtn = (Button)findViewById(R.id.test_btn);
        testBtn2 = (Button)findViewById(R.id.test_two);
        testBtn3 = (Button)findViewById(R.id.test_three);
        testBtn4 = (Button)findViewById(R.id.test_four);

    }

    @SuppressLint("ResourceAsColor")
    @Override
    protected void initView() {
        testBtn.setOnClickListener(this);
        testBtn.setBackgroundColor(R.color.color_aa1247c2);

        testBtn2.setOnClickListener(this);
        testBtn2.setBackgroundColor(R.color.color_aa1247c2);

        testBtn3.setOnClickListener(this);
        testBtn3.setBackgroundColor(R.color.color_aa1247c2);

        testBtn4.setOnClickListener(this);
        testBtn4.setBackgroundColor(R.color.color_aa1247c2);
    }

    @Override
    protected void setInstance() {
        instance = this;
    }

    @Override
    protected void removeInstance() {
        instance = null;
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.test_btn:

//                //scan
                Intent intent = new Intent();
                intent.putExtra(BluetoothConstants.Scan.BES_SCAN, BluetoothConstants.Scan.SCAN_BLE);
                ActivityUtils.gotoActForResult(intent, BluetoothConstants.Scan.REQUEST_CODE_SCAN, instance, ScanActivity.class);

                break;

            case R.id.test_two:
                RemoteOTAConfig config = new RemoteOTAConfig();
                String path = "/storage/emulated/0/bin/roleswitch.bin";//"/storage/emulated/0/bin/best2300p_user_fw.bin";///storage/emulated/0/Android/data/com.bes.besall/files/ss/ss
                config.setLocalPath(path);
                otaTask.setOtaConfig(config);
                break;

            case R.id.test_three:
                OTADfuInfo otaDfuInfo = new OTADfuInfo("001", 1);
                otaTask.startDataTransfer(otaDfuInfo, instance);
                break;

            case R.id.test_four:
                otaTask.stopDataTransfer();
                break;
            default:
                break;
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data)
    {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == BluetoothConstants.Scan.REQUEST_CODE_SCAN) {
            onPickDevice(resultCode, data);
        }

    }

    private void onPickDevice(int resultCode, Intent data)
    {
        if (resultCode == RESULT_OK) {
            Log.i(TAG, "onPickDevice: -----" + mDevice.getName());
            HmDevice hmDevice = (HmDevice) data.getSerializableExtra(BES_SCAN_RESULT);
            mDevice = BtHeleper.getBluetoothAdapter(instance).getRemoteDevice(hmDevice.getPreferredProtocol() == DeviceProtocol.PROTOCOL_BLE ? hmDevice.getBleAddress() : hmDevice.getDeviceMAC());


            BesServiceConfig serviceConfig = new BesServiceConfig();
            serviceConfig.setDeviceProtocol(DeviceProtocol.PROTOCOL_BLE);
            serviceConfig.setDevice(hmDevice);
            serviceConfig.setTotaConnect(false);
            besOtaService = new BesOtaService(serviceConfig, instance, instance);
            otaTask = besOtaService;
        }
    }

//BesServiceListener
    @Override
    public void onTotaConnectState(boolean state, HmDevice hmDevice) {
        Log.i(TAG, "onTotaConnectState: -----" + state);

    }

    @Override
    public void onErrorMessage(int msg, HmDevice hmDevice) {
        Log.i(TAG, "onErrorMessage: -----" + msg);
    }

    @Override
    public void onStateChangedMessage(int msg, String msgStr, HmDevice hmDevice) {
        Log.i(TAG, "onStateChangedMessage: ----" + msgStr);
        if (msg == BesSdkConstants.BES_CONNECT_SUCCESS) {
            Log.i(TAG, "onStateChangedMessage: -----BES_CONNECT_SUCCESS");
        } else if (msg == BesSdkConstants.BES_CONNECT_ERROR) {
            
        }

    }

    @Override
    public void onSuccessMessage(int msg, HmDevice hmDevice) {

    }

    @Override
    public void onOTAStatusChanged(OTAStatus newStatus, HmDevice hmDevice) {

    }

    @Override
    public void onOTAProgressChanged(float progress, HmDevice hmDevice) {
        Log.i(TAG, "onOTAProgressChanged: -----------" + progress);
    }
}
