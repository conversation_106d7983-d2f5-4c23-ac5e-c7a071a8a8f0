package com.bes.sdk.message;

import java.io.Serializable;

/**
 * Anc status definition
 */
public class AncSettings implements Serializable
{
    public static final int INVALID_VALUE = -1;
    public static final int ANC_SPORTS_OFF = 0;
    public static final int ANC_SPORTS_ON = 1;
    public static final int FILTER_OFF = 0;
    public static final int FILTER_ANC = 1;
    public static final int FILTER_TALK_THROUGH = 2;
    public static final int FILTER_AMBIENT_AWARE = 3;

    /**
     * filter:
     * <li>{@link #FILTER_OFF} - OFF;
     * <li>{@link #FILTER_ANC} - ANC;
     * <li>{@link #FILTER_TALK_THROUGH} - TalkThrough;
     * <li>{@link #FILTER_AMBIENT_AWARE} - AmbientAware.
     */
    private int filter;

    /**
     * gain: for specified filter, double value.
     */
    private double gain;

    /**
     * ANC sports mode, only effect on {@link #filter} ANC filter.
     * <li>{@link #INVALID_VALUE} - doesn't support ANC sports mode.
     * <li>{@link #ANC_SPORTS_OFF} - off
     * <li>{@link #ANC_SPORTS_ON} - on
     */
    private int ancSportsMode = ANC_SPORTS_OFF;

    /**
     * @see #getFilter()
     * @param filter
     */
    public void setFilter(int filter) {
        this.filter = filter;
    }

    /**
     * @see #getGain()
     * @param gain
     */
    public void setGain(double gain) {
        this.gain = gain;
    }

    /**
     * filter:
     * <li>{@link #FILTER_OFF} - OFF;
     * <li>{@link #FILTER_ANC} - ANC;
     * <li>{@link #FILTER_TALK_THROUGH} - TalkThrough;
     * <li>{@link #FILTER_AMBIENT_AWARE} - AmbientAware.
     */
    public int getFilter() {
        return filter;
    }

    /**
     * gain: for specified filter, 0 by default.
     */
    public double getGain() {
        return gain;
    }

    /**
     * Get ANC sports mode, status stored in device storage, {@link #ancSportsMode} only effect on {@link #filter} ANC filter.
     * @return default value is {@link #ANC_SPORTS_OFF}
     * <li>{@link #INVALID_VALUE} - doesn't support ANC sports mode.
     * <li>{@link #ANC_SPORTS_OFF} - off
     * <li>{@link #ANC_SPORTS_ON} - on
     */
    public int getAncSportsMode() {
        return ancSportsMode;
    }

    public void setAncSportsMode(int ancSportsMode) {
        this.ancSportsMode = ancSportsMode;
    }

    @Override
    public String toString() {
        return "AncSettings{" +
                "filter=" + filter +
                ", gain=" + gain +
                ", ancSportsMode=" + ancSportsMode +
                '}';
    }
}
