package com.besall.allbase.view.activity.chipstoollevel4.customerdial.makedial;

import android.content.Intent;

import com.besall.allbase.common.utils.ActivityUtils;
import com.besall.allbase.view.base.BasePresenter;


/**
 * <AUTHOR>
 * @time $ $
 */
class MakeDialPresenter extends BasePresenter<IMakeDialActivity> implements IMakeDialPresenter {
    public final String TAG = getClass().getSimpleName();


    @Override
    public void cropPhoto(MakeDialActivity activity, int requestCode) {
        Intent intent = new Intent();
        ActivityUtils.gotoActForResult(intent, requestCode, activity, CropPhotoActivity.class);
    }
}
