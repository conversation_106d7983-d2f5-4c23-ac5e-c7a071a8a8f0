<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_gravity="center">

    <ImageView
        android:id="@+id/image_item"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        />

    <LinearLayout
        android:id="@+id/function_item_bottom"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|center_horizontal"
        android:layout_marginBottom="30dp"
        android:orientation="vertical"
        android:visibility="gone">

        <TextView
            android:id="@+id/date_textview_bottom"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="FRI 18"
            android:textAlignment="center"
            android:textColor="@color/white"
            android:textSize="20sp" />

        <TextView
            android:id="@+id/time_textview_bottom"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="10 : 08"
            android:textAlignment="center"
            android:textColor="@color/white"
            android:textSize="30sp" />

    </LinearLayout>

    <LinearLayout
        android:id="@+id/function_item_icon"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="bottom|center"
        android:layout_marginTop="30dp"
        android:visibility="gone">

        <ImageView
            android:layout_width="100dp"
            android:layout_height="100dp"
            android:src="@drawable/xitongshijian"/>

    </LinearLayout>

</FrameLayout>