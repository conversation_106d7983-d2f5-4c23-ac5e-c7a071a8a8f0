# Add project specific ProGuard rules here.
# You can control the set of applied configuration files using the
# proguardFiles setting in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# If your project uses WebView with JS, uncomment the following
# and specify the fully qualified class name to the JavaScript interface
# class:
#-keepclassmembers class fqcn.of.javascript.interface.for.webview {
#   public *;
#}

# Uncomment this to preserve the line number information for
# debugging stack traces.
#-keepattributes SourceFile,LineNumberTable

# If you keep the line number information, uncomment this to
# hide the original source file name.
#-renamesourcefileattribute SourceFile
#*******************通用配置********************************
#代码混淆压缩比，在0~7之间，默认为5，一般不做修改
-optimizationpasses 5
# 不进行预校验，可加快混淆速度
-dontpreverify
# Optimization is turned off by default. Dex does not like code run
# through the ProGuard optimize and preverify steps (and performs some
# of these optimizations on its own).
# 关闭优化（原因见上边的原英文注释）
-dontoptimize
#把混淆类中的方法名也混淆了
-useuniqueclassmembernames
-dontusemixedcaseclassnames  # 混淆后类型都为小写
-verbose # 混淆时记录日志

-dontwarn com.koushikdutta.**
-dontwarn org.zeroturnaround.zip.**
-dontwarn org.slf4j.**
-dontwarn rx.internal.util.unsafe.**
-dontwarn com.liulishuo.filedownloader.**
-dontwarn okio.**
-dontwarn com.google.**
-dontwarn android.telephony.**


# 忽略混淆
-keep class com.bes.d3dsdk.** { *; }
-keep class com.bes.sdk.core.** { *; }
-keep class com.bes.bessdk.BesSdkConstants { *; }
