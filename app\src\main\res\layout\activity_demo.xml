<?xml version="1.0" encoding="utf-8"?>

<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:padding="20dp">

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvDevice"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/change_device"
        android:textColor="@color/black"
        android:textSize="16sp"
        app:layout_constraintBottom_toBottomOf="@id/btDeviceSelect"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/btDeviceSelect" />

    <androidx.appcompat.widget.AppCompatButton
        android:id="@+id/btDeviceSelect"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@color/teal_200"
        android:text="@string/choose"
        android:textColor="@color/white"
        android:textSize="16sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvFileSelect"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/filepath"
        android:textColor="@color/black"
        android:textSize="16sp"
        app:layout_constraintBottom_toBottomOf="@id/btFileSelect"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/btFileSelect" />

    <androidx.appcompat.widget.AppCompatButton
        android:id="@+id/btFileSelect"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="12dp"
        android:background="@color/teal_200"
        android:text="@string/choose"
        android:textColor="@color/white"
        android:textSize="16sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/btDeviceSelect"
        android:visibility="invisible"/>
    <ScrollView
        app:layout_constraintBottom_toTopOf="@+id/tvLog"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        android:id="@+id/ota_log_scrollview"
        android:layout_width="match_parent"
        android:layout_height="300dp"
        android:layout_marginBottom="20dp"
        >
        <TextView
            android:id="@+id/otaLogText"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginLeft="20dp"
            android:layout_marginRight="20dp"
            android:textSize="14sp"
            android:fontFamily="@font/sanshan_normal"
            android:textColor="@color/title_color_light"
            >

        </TextView>
    </ScrollView>

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvLog"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="20dp"
        android:text="log"
        android:textColor="#FF00FF"
        android:textSize="20sp"
        app:layout_constraintBottom_toTopOf="@+id/tvState"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />
    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvState"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="20dp"
        android:text="@string/current_ota_progress"
        android:textColor="#FF00FF"
        android:textSize="20sp"
        app:layout_constraintBottom_toTopOf="@+id/tvOtaUpdate"
        app:layout_constraintStart_toStartOf="parent" />


    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvOtaUpdate"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginBottom="28dp"
        android:background="@color/purple_500"
        android:gravity="center"
        android:padding="16dp"
        android:text="@string/start_ota"
        android:textColor="@color/white"
        android:textSize="18sp"
        app:layout_constraintBottom_toTopOf="@+id/tvOtaUpdateCancel"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />
    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvOtaUpdateCancel"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginBottom="28dp"
        android:background="@color/purple_500"
        android:gravity="center"
        android:padding="16dp"
        android:text="@string/cancel"
        android:textColor="@color/white"
        android:textSize="18sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>
