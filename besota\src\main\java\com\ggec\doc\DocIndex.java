package com.ggec.doc;

/**
 * YOTA SDK 文档索引
 * ===============
 * 
 * 本索引列出了 YOTA SDK 的所有文档文件，方便用户查阅。
 * 
 * 1. README.md
 * -----------
 * 一个完整的 Markdown 格式的 SDK 使用说明文档，包含集成步骤、API 参考和最佳实践。
 * 
 * 2. YOTAApiGuide.java
 * ------------------
 * 详细的 SDK 使用指南，通过 JavaDoc 方式提供，包含各种使用场景的代码示例。
 * 
 * 3. YOTAApiDemo.java
 * -----------------
 * 一个完整的 Demo 示例应用，展示了 YOTA SDK 的实际使用方法和最佳实践。
 * 
 * 4. 集成步骤总结
 * -------------
 * 
 * 1) 将 besota-release.aar 添加到项目的 libs 目录
 * 2) 在 build.gradle 中添加依赖
 * 3) 配置必要的权限
 * 4) 初始化 YOTAApi 实例
 * 5) 设置监听器
 * 6) 调用 API 开始升级
 * 
 * 5. API 调用流程
 * -------------
 * 
 * 1) 初始化: YOTAApi yotaApi = YOTAManager.getInstance()
 * 2) 设置监听器: yotaApi.setProgressListener() 和 yotaApi.setStatusListener()
 * 3) 开始升级: yotaApi.startUpgrade()
 * 4) 监听回调: 处理进度更新、状态变化和结果
 * 5) (可选) 取消升级: yotaApi.cancelUpgrade()
 * 
 * 6. 联系我们
 * ----------
 * 
 * 如有任何问题或需要技术支持，请联系:
 * - 邮箱: <EMAIL>
 * - 电话: 400-123-4567
 */
public class DocIndex {
    // 此类仅作为文档索引使用，不包含实际实现
} 