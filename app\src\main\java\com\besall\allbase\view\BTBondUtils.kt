package com.besall.allbase.view

import android.annotation.SuppressLint
import android.bluetooth.BluetoothDevice
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.graphics.*
import android.util.Log
import com.bes.sdk.core.OtaLog
import com.besall.allbase.R
import com.besall.allbase.app.MyApplication
import com.besall.allbase.view.activity.chipstoollevel4.ota.DemoActivity
import java.io.File
import java.io.Serializable

/**
 *
 * <AUTHOR>
 * @date 2023/6/14 18:21
 */
@SuppressLint("MissingPermission")
object BTBondUtils {

    var mDevice: BluetoothDevice? = null
    private var isRegisterBoneReceiver = false


    fun ByteArray.readUint32(offset: Int): Int {
        val i3 = this[offset + 3].toInt() and 0xFF shl 24
        val i2 = this[offset + 2].toInt() and 0xFF shl 16
        val i1 = this[offset + 1].toInt() and 0xFF shl 8
        val i0 = this[offset + 0].toInt() and 0xFF
        return (i3 or i2 or i1 or i0);
    }

    fun ByteArray.readUint32Little(offset: Int): Int {
        val i3 = this[offset + 0].toInt() and 0xFF shl 24
        val i2 = this[offset + 1].toInt() and 0xFF shl 16
        val i1 = this[offset + 2].toInt() and 0xFF shl 8
        val i0 = this[offset + 3].toInt() and 0xFF
        return (i3 or i2 or i1 or i0);
    }

    val HEX_ARRAY = "0123456789ABCDEF".toCharArray()
    private fun ByteArray.toHex(offset: Int, len: Int): String {
        val out = CharArray(len * 2)
        for (j in 0 until len) {
            val v = this[offset + j].toInt() and 0xFF
            out[j * 2] = HEX_ARRAY[v ushr 4]
            out[j * 2 + 1] = HEX_ARRAY[v and 0x0F]
        }
        return String(out)
    }

    private fun ByteArray.toUtf8String(offset: Int, len: Int): String {
        val out = ByteArray(len)
        for (j in 0 until len) {
            out[j] = this[j + offset]
        }
        return String(out).trim()
    }

    private fun ByteArray.readBytes(offset: Int, len: Int): ByteArray {
        val out = ByteArray(len)
        for (j in 0 until len) {
            out[j] = this[j + offset]
        }
        return out
    }

    private fun ByteArray.writeUtf8String(offset: Int, len: Int, text: String) {
        val textBytes = text.toByteArray(Charsets.UTF_8)
        val max = Math.min(textBytes.size, len)
        for (i in 0 until max) {
            this[i + offset] = textBytes[i]
        }
    }

    private fun ByteArray.writeInt32(offset: Int, value: Int) {
        this[offset + 3] = (value shr 24 and 0xFF).toByte()
        this[offset + 2] = (value shr 16 and 0xFF).toByte()
        this[offset + 1] = (value shr 8 and 0xFF).toByte()
        this[offset] = (value and 0xFF).toByte()
    }

    const val HEAD_MAGIC = 0x1234abcd
    const val FOOTER_MAGIC = 0xa5a5a5a5
    const val HEAD_INFO_LENGTH = 256
    const val HEAD_SUBFILE_INFO_LENGTH = 76
    const val HEAD_FILE_NAME_MAX = 48
    const val HEAD_AUTHOR_MAX = 16
    const val HEAD_VERSION_MAX = 24
    const val HEAD_RESERVE_LENGTH = 20
    const val HEAD_SUBFILE_NAME_MAX = 64
    const val FOOTER_INFO_LENGTH = 128
    const val kFOOTER_RESERVE_LENGTH = 100

    fun testBuildDialDfu(){
        val bgBitmap = BitmapFactory.decodeResource(
            MyApplication.getContext().resources,
            R.drawable.bg_37635_0_fff8b62d,
            BitmapFactory.Options().apply {
                inScaled = false
            }
        )
        BesDialUtils.magickImageCommande("testbg.png", bgBitmap, MyApplication.getContext())
        val thBitmap = BitmapFactory.decodeResource(
            MyApplication.getContext().resources,
            R.drawable.thumb_37635_0_fff8b62d,
            BitmapFactory.Options().apply {
                inScaled = false
            }
        )
        BesDialUtils.magickImageCommande("testth.png", thBitmap, MyApplication.getContext())
//        val dfuBytes =
//            buildDialDfu(32768 + 2, android.graphics.Color.RED, 1, ArrayList<BinInfo>().apply {
//            add(BinInfo().apply {
//                this.binBytes = DemoActivity.getIndex8(thumbnailBitmapOutFile)
//                this.name = "img_dial_thumbnail.png"
//                this.crc16 = DemoActivity.crc_16_CCITT_False(this.binBytes)
//                this.size = this.binBytes!!.size
//            })
//            add(BinInfo().apply {
//                this.binBytes = DemoActivity.getIndex8(outImagePath)
//                this.name = "img_dial_background.png"
//                this.crc16 = DemoActivity.crc_16_CCITT_False(this.binBytes)
//                this.size = this.binBytes!!.size
//            })
//        })

//        val dfuFile = File("/storage/emulated/0/Android/data/com.bes.besall/files/out.dfu")
//        if (!dfuFile.exists()) {
//            dfuFile.createNewFile()
//        }
//        val fos = FileOutputStream(dfuFile)
//        fos.write(dfuBytes)
//        fos.close()

    }

    //-生成表盘dfu的bin文件
    fun buildDialDfu(
        id: Int,
        color: Int,
        style: Int,
        binList: List<BinInfo>
    ):ByteArray {
//        val img_dial_thumbnail_Byte = img_dial_thumbnail.readBytes()
//        val img_dial_background_Byte = img_dial_background.readBytes()
        var offset = 0
        val headBytes = ByteArray(HEAD_INFO_LENGTH)
        val headFileBytes = ByteArray(HEAD_SUBFILE_INFO_LENGTH * binList.size)
        var binAddr = headBytes.size + headFileBytes.size

        for (i in binList.indices) {
            val bin = binList[i]
            var binOffset = i * HEAD_SUBFILE_INFO_LENGTH
            headFileBytes.writeInt32(binOffset, bin.crc16)
            binOffset += 4

            headFileBytes.writeInt32(binOffset, binAddr)
            binOffset += 4

            headFileBytes.writeInt32(binOffset, bin.size)
            binOffset += 4

            headFileBytes.writeUtf8String(binOffset, HEAD_SUBFILE_NAME_MAX, bin.name)
            binOffset += HEAD_SUBFILE_NAME_MAX

            binAddr += bin.size
        }
        val footerBytes = ByteArray(FOOTER_INFO_LENGTH)
        footerBytes.writeInt32(0, FOOTER_MAGIC.toInt())
        footerBytes.writeUtf8String(4,HEAD_VERSION_MAX,"")

//uint32_t magic;
        headBytes.writeInt32(offset, HEAD_MAGIC)
        offset += 4
//uint32_t headSize;
        headBytes.writeInt32(offset, headBytes.size + headFileBytes.size)
        offset += 4

//uint32_t footerSize;
        headBytes.writeInt32(offset, footerBytes.size)
        offset += 4
//uint8_t name[HEAD_FILE_NAME_MAX];
        headBytes.writeUtf8String(offset, HEAD_FILE_NAME_MAX, "ls17_custom_dial.dfu");
        offset += HEAD_FILE_NAME_MAX
        //uint8_t author[HEAD_AUTHOR_MAX]; //bin 文件授权信息
        headBytes.writeUtf8String(offset, HEAD_AUTHOR_MAX, "haylou_android");
        offset += HEAD_AUTHOR_MAX
//uint8_t version[HEAD_VERSION_MAX]; //bin 文件版本号
        headBytes.writeUtf8String(offset, HEAD_VERSION_MAX, "1");
        offset += HEAD_VERSION_MAX
//uint32_t subcode; //bin 文件子版本号(递加版本)
        headBytes.writeInt32(offset, 1)
        offset += 4
//uint32_t crc16; //bin 文件校验码
        headBytes.writeInt32(offset, 1)
        offset += 4
//uint32_t length; //bin 文件中所有图片数据的累加长度
        var length = 0
        binList.forEach {
            length += it.binBytes!!.size
        }
        headBytes.writeInt32(offset, length)
        offset += 4

//uint32_t fileNum; //bin 文件中图片的个数,同时也表示有多少个文件头
        headBytes.writeInt32(offset, binList.size)
        offset += 4
//ID
        headBytes.writeInt32(offset, id)
        offset += 4

        //color
        headBytes.writeInt32(offset, color)
        offset += 4

        //style
        headBytes.writeInt32(offset, style)
        offset += 4


        val dfuBytes = ByteArray(headBytes.size + headFileBytes.size + footerBytes.size + length)
        var copyOffset = 0
        System.arraycopy(headBytes, 0, dfuBytes, copyOffset, headBytes.size)
        copyOffset += headBytes.size
        System.arraycopy(headFileBytes, 0, dfuBytes, copyOffset, headFileBytes.size)
        copyOffset += headFileBytes.size
        for (i in binList.indices) {
            val bin = binList[i]
            System.arraycopy(bin.binBytes!!, 0, dfuBytes, copyOffset, bin.binBytes!!.size)
            copyOffset += bin.binBytes!!.size
        }
        System.arraycopy(footerBytes, 0, dfuBytes, copyOffset, footerBytes.size)
        copyOffset += footerBytes.size
        return dfuBytes
    }

    //CDAB34126F74615F6C69622E4446550000000000000000000000000000000000000000000000000000000000000000000000000067616F79756570696E670000000000007665722D323032332F30362F32362031353A35383A30380001000000FFFFFFFF5BBB9700020000000000000000000000000000000000000000000000
    fun readDfu(dfuFile: File) {
        val fileBytes = dfuFile.readBytes()
        if (fileBytes.size <= 128 * 2) return
        var offset = 0
        val magic = fileBytes.readUint32(0)
        offset += 4

        if (magic == HEAD_MAGIC) {
            Log.d("TAG", "magic校验成功")
        }
        val headSize = fileBytes.readUint32(offset)
        offset += 4

        val footerSize = fileBytes.readUint32(offset)
        offset += 4

        val name = fileBytes.toUtf8String(offset, HEAD_FILE_NAME_MAX)
        offset += HEAD_FILE_NAME_MAX

        val author = fileBytes.toUtf8String(offset, HEAD_AUTHOR_MAX)
        offset += HEAD_AUTHOR_MAX

        val version = fileBytes.toUtf8String(offset, HEAD_VERSION_MAX)
        offset += HEAD_VERSION_MAX

        val subCode = fileBytes.readUint32(offset)
        offset += 4

        val crc16 = fileBytes.readUint32(offset)
        offset += 4

        val length = fileBytes.readUint32(offset)
        offset += 4

        val fileNum = fileBytes.readUint32(offset)
        offset += 4

        val reserve = fileBytes.readBytes(offset, HEAD_RESERVE_LENGTH)
        //偏移到头
        offset = HEAD_INFO_LENGTH
        val binInfos = mutableListOf<BinInfo>()
        if (fileNum > 0) {
            var num = fileNum
            while (num > 0) {
                binInfos.add(BinInfo().apply {
                    this.crc16 = fileBytes.readUint32(offset)
                    offset += 4
                    this.addr = fileBytes.readUint32(offset)
                    offset += 4
                    this.size = fileBytes.readUint32(offset)
                    offset += 4
                    this.name = fileBytes.toUtf8String(offset, HEAD_SUBFILE_NAME_MAX)
                    offset += HEAD_SUBFILE_NAME_MAX
                })
                num--
            }
            for (binInfo in binInfos) {
                binInfo.binBytes = fileBytes.readBytes(binInfo.addr, binInfo.size)
//                val crc16 = DemoActivity.crc_16_CCITT_False(binInfo.binBytes)
//                if (crc16 != binInfo.crc16) {
//                    Log.e("sinyi", "readDfu: crc16 不匹配 $crc16  $binInfo")
//                }
            }
        }
        val footerBytes =
            fileBytes.readBytes(fileBytes.size - FOOTER_INFO_LENGTH, FOOTER_INFO_LENGTH)
        val footerMagic = footerBytes.readUint32(0)
        val footerVersion = footerBytes.toUtf8String(4, HEAD_VERSION_MAX)
        if (footerMagic == FOOTER_INFO_LENGTH) {
            Log.d("sinyi", "footerMagic: 校验成功")
        }

    }


    data class BinInfo(
        var crc16: Int = 0,
        var addr: Int = 0,
        var size: Int = 0,
        var name: String = "",
        var binBytes: ByteArray? = null
    ) {
        override fun equals(other: Any?): Boolean {
            if (this === other) return true
            if (javaClass != other?.javaClass) return false

            other as BinInfo

            if (crc16 != other.crc16) return false
            if (addr != other.addr) return false
            if (size != other.size) return false
            if (name != other.name) return false

            return true
        }

        override fun hashCode(): Int {
            var result = crc16
            result = 31 * result + addr
            result = 31 * result + size
            result = 31 * result + name.hashCode()
            return result
        }

        override fun toString(): String {
            return "BinInfo(crc16=$crc16, addr=$addr, size=$size, name='$name', binBytesSize=${binBytes?.size})"
        }
    }


    fun register(context: Context) {
        if (!isRegisterBoneReceiver) {
            context.registerReceiver(
                mBondingBroadcastReceiver,
                IntentFilter(BluetoothDevice.ACTION_BOND_STATE_CHANGED)
            )
        }
        isRegisterBoneReceiver = true
    }

    private var mSuccess: (() -> Unit)? = null
    private var mFailure: (() -> Unit)? = null

    fun requestBond(
        context: Context,
        btDevice: BluetoothDevice,
        success: () -> Unit,
        failure: () -> Unit
    ) {
        register(context)
        OtaLog.logE("发起配对")
        this.mDevice = btDevice
        this.mSuccess = success
        this.mFailure = failure
        //在这里发起配对
        val ret = mDevice!!.createBond()
        if (!ret) {
            OtaLog.logE("createBond ret false")
            failure.invoke()
            release(context)
        }
    }

    fun isBondDevice(btDevice: BluetoothDevice): Boolean {
        return btDevice.bondState == BluetoothDevice.BOND_BONDED
    }

    fun release(context: Context) {
        isRegisterBoneReceiver = false
        try {
            context.unregisterReceiver(mBondingBroadcastReceiver)
        } catch (e: Exception) {
        }
    }

    fun test() {
        val contactList: MutableList<ContactInfo> = ArrayList()
        contactList.add(ContactInfo("1", "C", "111"))
        contactList.add(ContactInfo("1", "A", "111"))
        contactList.add(ContactInfo("1", "XXX", "111"))
        contactList.add(ContactInfo("1", "刘xx", "111"))
        contactList.add(ContactInfo("1", "习单独", "111"))
        contactList.add(ContactInfo("1", "习AA", "111"))
        contactList.add(ContactInfo("1", "习AB", "111"))
        val softList = contactList.sortedBy {
            it.name
        }
    }
    data class ContactInfo(
        val id: String,
        val name: String,
        val number: String,
        val colorR: Int = 0,
        val colorG: Int = 0,
        val colorB: Int = 0
    ) : Serializable
    private val mBondingBroadcastReceiver: BroadcastReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            val device = intent!!.getParcelableExtra<BluetoothDevice>(BluetoothDevice.EXTRA_DEVICE)
            val bondState = intent.getIntExtra(BluetoothDevice.EXTRA_BOND_STATE, -1)
            val previousBondState =
                intent.getIntExtra(BluetoothDevice.EXTRA_PREVIOUS_BOND_STATE, -1)
            if (mDevice == null || device == null || context == null) return
            if (device.address != mDevice!!.address) return
            when (bondState) {
                BluetoothDevice.BOND_NONE -> {
                    mFailure?.invoke()
                    release(context)
                }
                BluetoothDevice.BOND_BONDING -> {}
                BluetoothDevice.BOND_BONDED -> {
                    mSuccess?.invoke()
                    release(context)
                }
            }
        }
    }

}