# YOTA SDK 使用指南

## 1. 简介

YOTA SDK 提供了蓝牙设备 OTA 升级的完整解决方案，支持固件升级、进度监控和状态回调等功能。本文档将帮助开发者快速理解和集成 YOTA SDK。

## 2. 功能特点

- 支持蓝牙设备固件 OTA 升级
- 实时监控升级进度
- 完整的状态回调机制
- 简单易用的 API 接口

## 3. 集成步骤

### 3.1 添加依赖

将 besota-release.aar 文件添加到项目的 libs 目录，并在 build.gradle 中添加依赖：

```gradle
dependencies {
    implementation files('libs/besota-release.aar')
}
```

### 3.2 权限配置

在 AndroidManifest.xml 中添加必要的权限：

```xml
<!-- 蓝牙相关权限 -->
<uses-permission android:name="android.permission.BLUETOOTH" />
<uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
<uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
<uses-permission android:name="android.permission.BLUETOOTH_SCAN" />

<!-- 文件存储权限 -->
<uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
<uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
```

## 4. API 使用指南

### 4.1 初始化 SDK

```java
// 获取 YOTA SDK 实例
YOTAApi yotaApi = YOTAManager.getInstance();
```

### 4.2 开始升级

```java
// 设备参数
HmDevice device = ...; // 通过蓝牙搜索获取的设备对象
String filePath = "/sdcard/firmware.bin"; // 升级固件路径

// 开始升级
boolean result = yotaApi.startUpgrade(device, filePath);
if (result) {
    Log.d("YOTA", "升级流程已启动");
} else {
    Log.e("YOTA", "启动升级失败");
}
```

### 4.3 监听升级进度

```java
yotaApi.setProgressListener(new YOTAApi.ProgressListener() {
    @Override
    public void onProgressChanged(float progress) {
        // 进度范围 0-100
        Log.d("YOTA", "当前升级进度: " + progress + "%");
        // 更新 UI 进度条
        progressBar.setProgress((int)progress);
    }
});
```

### 4.4 监听状态变化

```java
yotaApi.setStatusListener(new YOTAApi.StatusListener() {
    @Override
    public void onStatusChanged(OTAStatus status) {
        switch (status) {
            case CONNECTING:
                Log.d("YOTA", "正在连接设备...");
                break;
            case TRANSFERRING:
                Log.d("YOTA", "正在传输数据...");
                break;
            case VERIFYING:
                Log.d("YOTA", "正在验证固件...");
                break;
            // 处理其他状态...
        }
    }
    
    @Override
    public void onError(int errorCode, String message) {
        Log.e("YOTA", "升级出错: " + message + " (错误码: " + errorCode + ")");
        // 显示错误信息
        showErrorDialog(message);
    }
    
    @Override
    public void onSuccess() {
        Log.d("YOTA", "升级成功完成!");
        // 显示升级成功提示
        showSuccessDialog();
    }
});
```

### 4.5 取消升级

```java
// 用户取消或需要中断升级时调用
boolean cancelResult = yotaApi.cancelUpgrade();
if (cancelResult) {
    Log.d("YOTA", "升级已取消");
} else {
    Log.e("YOTA", "取消升级失败");
}
```

## 5. 完整示例

```java
// 1. 获取 SDK 实例
YOTAApi yotaApi = YOTAManager.getInstance();

// 2. 设置监听器
yotaApi.setProgressListener(progressListener);
yotaApi.setStatusListener(statusListener);

// 3. 启动升级
boolean result = yotaApi.startUpgrade(hmDevice, firmwarePath);

// 4. 处理用户取消场景
cancelButton.setOnClickListener(v -> {
    yotaApi.cancelUpgrade();
});
```

## 6. API 参考

### YOTAApi

| 方法 | 描述 |
| --- | --- |
| `boolean startUpgrade(HmDevice device, String filePath)` | 开始升级过程，返回是否成功启动 |
| `void setProgressListener(ProgressListener listener)` | 设置进度监听器 |
| `void setStatusListener(StatusListener listener)` | 设置状态监听器 |
| `boolean cancelUpgrade()` | 取消正在进行的升级，返回是否成功取消 |

### ProgressListener

| 方法 | 描述 |
| --- | --- |
| `void onProgressChanged(float progress)` | 当进度更新时回调，progress 范围 0-100 |

### StatusListener

| 方法 | 描述 |
| --- | --- |
| `void onStatusChanged(OTAStatus status)` | 当状态改变时回调 |
| `void onError(int errorCode, String message)` | 当发生错误时回调 |
| `void onSuccess()` | 当升级成功完成时回调 |

## 7. 注意事项

- 确保在调用 startUpgrade 前已经设置好监听器
- 升级过程中避免应用退出到后台，可能导致升级中断
- 确保设备电量充足，避免在升级过程中关机
- 升级文件必须是有效的固件包，否则可能导致设备无法启动
- 升级过程中保持设备与手机距离较近，避免蓝牙断开

## 8. 常见问题

### Q: 升级过程中断开连接怎么办？
A: SDK 会自动尝试重新连接，如果连接失败，会通过 StatusListener 的 onError 方法返回错误信息。

### Q: 如何获取正确的固件文件？
A: 请联系设备厂商获取官方固件文件，使用非官方固件可能导致设备损坏。

### Q: 升级过程中可以关闭应用吗？
A: 不建议在升级过程中关闭应用，这可能导致升级中断或固件损坏。

## 9. 技术支持

如有任何问题，请联系技术支持团队：<EMAIL> 