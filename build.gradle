// Top-level build file where you can add configuration options common to all sub-projects/modules.
buildscript {
    ext {
        kotlin_version = '1.6.21'
    }
    repositories {
        //阿里云镜像
        maven {
            allowInsecureProtocol true
            url 'https://maven.aliyun.com/nexus/content/repositories/google' }
        maven { url 'https://maven.aliyun.com/repository/public/' }
        google()
        jcenter()
    }
    dependencies {
        classpath "com.android.tools.build:gradle:4.1.0"
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"

        // NOTE: Do not place your application dependencies here; they belong
        // in the individual module build.gradle files
    }

}

allprojects {
    repositories {
        //阿里云镜像
        maven {
            allowInsecureProtocol true
            url 'https://maven.aliyun.com/nexus/content/repositories/google' }
        maven { url 'https://maven.aliyun.com/repository/public/' }
        google()
        jcenter()
    }
}

task clean(type: Delete) {
    delete rootProject.buildDir
}