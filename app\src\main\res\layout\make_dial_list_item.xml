<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="150dp"
    android:layout_height="150dp"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_gravity="center"
    android:gravity="center">

    <View
        android:id="@+id/image_item_big"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="fitStart"
        android:adjustViewBounds="true"
        android:background="@color/lineview"
        />

    <include layout="@layout/dial_show_bgview"
        android:layout_width="150dp"
        android:layout_height="150dp"
        android:scaleType="fitStart"
        android:adjustViewBounds="true"
        />

</androidx.constraintlayout.widget.ConstraintLayout>