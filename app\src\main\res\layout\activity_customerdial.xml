<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    android:background="@mipmap/bes_bg6">

    <include
        android:id="@+id/tool"
        layout="@layout/toolbar"
        />

    <include
        android:id="@+id/loginfo"
        layout="@layout/logview"
        android:visibility="gone"/>

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginBottom="20dp">
        <LinearLayout
            android:layout_width="fill_parent"
            android:layout_height="fill_parent"
            android:orientation="vertical"
            android:padding="2dp">

            <TextView
                android:id="@+id/current_device"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="30dp"
                android:padding="5dp"
                android:textColor="@color/btnDisableColor"
                android:textSize="13sp"
                android:textAlignment="center"
                />
            <Button
                android:id="@+id/choose_device"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:background="@drawable/ota_click"
                android:text="@string/change_device_tota_spp"
                android:textAllCaps="false"
                android:textColor="@color/white"
                />

            <ImageView
                android:id="@+id/show_dial_photo"
                android:layout_width="160dp"
                android:layout_height="190dp"
                android:layout_gravity="center"
                android:visibility="gone"
                />

            <Button
                android:id="@+id/choose_dial"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:background="@drawable/ota_click"
                android:text="@string/choose_customer_dial"
                android:textAllCaps="false"
                android:textColor="@color/white"
                />

            <HorizontalScrollView
                android:background="#CCCCFF"
                android:layout_width="wrap_content"
                android:layout_height="50dp"
                android:layout_marginLeft="20dp"
                android:layout_marginRight="20dp"
                android:fadeScrollbars="false">

                <RadioGroup
                    android:id="@+id/radio_group_file_type"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <TextView
                        android:text="@string/choose_online_dial"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"/>

                    <RadioButton
                        android:id="@+id/radio_button_file_type_online"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        />

                    <TextView
                        android:text="@string/choose_picture"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="10dp"/>

                    <RadioButton
                        android:id="@+id/radio_button_file_type_picture"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        />

                    <TextView
                        android:text="@string/choose_font_library"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="10dp"/>

                    <RadioButton
                        android:id="@+id/radio_button_file_type_font"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        />

                    <TextView
                        android:text="@string/choose_tp_firmware"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="10dp"/>

                    <RadioButton
                        android:id="@+id/radio_button_file_type_tp"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        />

                    <TextView
                        android:text="@string/choose_heart_rate_firmware"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="10dp"/>

                    <RadioButton
                        android:id="@+id/radio_button_file_type_heart_rate"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        />

                    <TextView
                        android:text="@string/choose_language_packet"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="10dp"/>

                    <RadioButton
                        android:id="@+id/radio_button_file_type_language"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        />

                </RadioGroup>

            </HorizontalScrollView>


            <TextView
                android:id="@+id/show_old_file_path"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:padding="5dp"
                android:textColor="@color/btnDisableColor"
                android:textSize="13sp"
                android:textAlignment="center"
                android:visibility="gone"
                />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/use_incremental_upgrade"
                    android:textAlignment="center"
                    android:textSize="16sp" />

                <com.suke.widget.SwitchButton
                    android:id="@+id/switchButton_use_diff_upgrade"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginLeft="5dp"
                    app:sb_show_indicator="false" />


            </LinearLayout>

            <TextView
                android:id="@+id/show_dial_dfu_path"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:padding="5dp"
                android:textColor="@color/btnDisableColor"
                android:textSize="13sp"
                android:textAlignment="center"
                />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center"
                android:weightSum="10">

                <Button
                    android:id="@+id/choose_old_file"
                    android:layout_width="0dp"
                    android:layout_weight="5"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:background="@drawable/ota_click"
                    android:text="@string/choose_old_version_file"
                    android:textAllCaps="false"
                    android:textColor="@color/white"
                    android:visibility="gone"
                    />
                <Button
                    android:id="@+id/choose_dfu_file"
                    android:layout_width="0dp"
                    android:layout_weight="5"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:background="@drawable/ota_click"
                    android:text="@string/choose_dfu_file"
                    android:textAllCaps="false"
                    android:textColor="@color/white"
                    />

            </LinearLayout>

            <Button
                android:id="@+id/start_transfer"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:background="@drawable/ota_button_bg_press"
                android:text="@string/start_transfer_tips"
                android:textAllCaps="false"
                android:textColor="@color/white"
                android:enabled="false"
                />

            <ProgressBar
                android:id="@+id/transfer_progress"
                android:layout_width="match_parent"
                android:layout_height="10dp"
                android:layout_marginLeft="20dp"
                android:layout_marginRight="20dp"
                android:max="100"
                android:progress="0"
                style="?android:attr/progressBarStyleHorizontal"
                />

            <TextView
                android:id="@+id/transfer_percent"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:textColor="@color/title_color_light"
                android:text="0"
                android:textSize="18sp"
                android:textAlignment="center"/>

        </LinearLayout>

    </ScrollView>

</LinearLayout>