package com.besall.allbase.view.activity.chipstoollevel4.checkcrc;

import android.content.Context;
import android.content.Intent;

import com.bes.bessdk.service.base.BesServiceConfig;
import com.bes.bessdk.service.base.BesServiceListener;
import com.bes.bessdk.service.checkcrc.CheckCrcService;
import com.besall.allbase.bluetooth.BluetoothConstants;
import com.besall.allbase.bluetooth.scan.ScanActivity;
import com.besall.allbase.common.utils.ActivityUtils;
import com.besall.allbase.view.base.BasePresenter;

/**
 * <AUTHOR>
 * @time $ $
 */
class CheckCrcPresenter extends BasePresenter<ICheckCrcActivity> implements ICheckCrcPresenter {

    private CheckCrcService checkCrcService;
    @Override
    public void pickDecice(CheckCrcActivity context, int scan) {
        Intent intent = new Intent();
        intent.putExtra(BluetoothConstants.Scan.BES_SCAN, scan);
        ActivityUtils.gotoActForResult(intent, BluetoothConstants.Scan.REQUEST_CODE_SCAN, context, ScanActivity.class);
    }

    @Override
    public void connectDevice(BesServiceConfig serviceConfig, BesServiceListener listener, Context context) {
        checkCrcService = new CheckCrcService(serviceConfig, listener, context);
    }

    @Override
    public void getCrc() {
        if (checkCrcService != null) {
            checkCrcService.getCrc();
        }
    }

    @Override
    public void factoryReset() {
        if (checkCrcService != null) {
            checkCrcService.factoryReset();
        }
    }

}
