<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="app_name">Bestechnic</string>
    <string name="activity_ble_ota">BLE OTA</string>
    <string name="activity_spp_ota">SPP OTA</string>
    <string name="activity_ble_ota_v1">BLE OTA 1.0</string>
    <string name="activity_spp_ota_v1">SPP OTA 1.0</string>
    <string name="activity_ble_ota_v2">BLE OTA 2.0</string>
    <string name="activity_spp_ota_v2">SPP OTA 2.0</string>
    <string name="activity_main">BES Toolbox</string>
    <string name="activity_bluetooth_scan">BLE SCAN</string>
    <string name="activity_classics_devices_scan">Classics BT SCAN</string>
    <string name="activity_USB_scan">USB SCAN</string>
    <string name="privacy_policy"> 《Agreement|Privacy Policy》</string>
    <string name="agree">Agree</string>
    <string name="disagree">Disagree</string>
    <string name="privacy_guide"> Privacy Guide: </string>
    <string name="agreement">
Bestechnic Privacy Policy and Terms of Use
        \nPublish Date: 2022/10/10
        \n\n"Bestechnic " is a Bluetooth chip testing tool provided by Hengxuan Technology (Shanghai) Co., Ltd. (hereinafter referred to as " we " ) . Your trust is very important to us. We are well aware of the importance of personal information to you. We will take corresponding security protection measures in accordance with the requirements of laws and regulations, and try our best to keep your personal information safe and controllable. In view of this, we have formulated this privacy policy (hereinafter referred to as " this policy " ) and remind you:
        \nThis application respects and protects the personal privacy of all users who use the service. In order to provide you with more accurate and personalized services, this application will use and disclose your personal information in accordance with the provisions of this Privacy Policy. However, this application will treat this information with a high degree of diligence and prudence, and will not send the information to a third party. The information can only be viewed locally. There is no network request set within the APP . Please read carefully and fully understand the terms of the " Service Agreement " and "Privacy Policy " . Including but not limited to : In order to provide you with services such as Bluetooth links and content sharing, we need to collect your device information, operation logs and other personal information. You can view, change, delete personal information and manage your authorization at any time in
        " Settings " . \n\nPart 1 Definitions
        \n\nPersonal information refers to electronic or other records that can be used alone or in combination with other information Various information that identifies the identity of a specific natural person or reflects the activities of a specific natural person.
        \n\nPersonal information deletion: refers to the act of removing personal information from the system involved in the realization of daily business functions, so that it remains in a state that cannot be retrieved or accessed .
\n\nWe collect data based on your interactions with us and the choices you make, including your privacy settings and the products and features you use.
\n\nPart II Privacy Policy
\n\nThis Sections will help you understand the following:
\n1. How we collect and use your information \n2. How we share, transfer, publicly disclose your information \n3. How we protect your personal information \n4. How you manage your Your personal information \n5. How to update this policy \n6. How to contact us
\n\n1. How we collect and use your information
\n\nWe need / may when you use our products and / or services Your personal information that needs to be collected and used: \nIn order to provide you with the basic functions of our products and / or services, you must authorize us to collect and use the necessary information. If you refuse to provide the corresponding information, you will not be able to function normally Use our products and / or services;
\nYou understand and agree: \n1 , We are committed to creating a variety of products and services to meet your needs. Because we provide you with a wide variety of products and services, and different users choose There are differences in the scope of the specific products / services used, and accordingly, the basic / additional functions and the type and scope of personal information collected and used will be different, please refer to the specific product / service functions;
\n2 , in order to bring you To provide a better product and service experience, we are constantly striving to improve our technology, and we may introduce new or optimized functions from time to time, which may require the collection and use of new personal information or changes in the purpose or method of using personal information. In this regard, we will separately explain to you the purpose, scope and use of the corresponding information by updating this policy, pop-up windows, page prompts, etc., and provide you with a way to choose your own consent, and after obtaining your express consent After collection and use. During this process, if you have any questions, comments or suggestions, you can contact us through the contact information provided by us, and we will answer you as soon as possible.
\n\nSecond , how do we Share, transfer, and publicly disclose your personal information
\n\nWe will not share your personal information with companies, organizations and individuals other than Hengxuan Technology service providers. In the following cases, share, transfer, and publicly disclose your personal information Information does not require your prior consent:
\n1 , related to national security and national defense security. \n2 , related to public safety, public health, and major public interests. \n3 , related to criminal investigation, prosecution, trial and execution of judgments. \n4 . For the protection of your or other personal life, property and other major legitimate rights and interests, but it is difficult to obtain my consent. \n5 . Your personal information disclosed to the public by yourself. \n6 . Collect personal information from legally publicly disclosed information, such as legal news reports, government information disclosure and other channels.
\n\n3 . How do we protect your personal information
\n\nIn order to ensure the security of your information, we have taken reasonable and feasible security protection measures in line with industry standards to protect the security of the personal information you provide and prevent your personal information from being compromised. Unauthorized access, public disclosure, use, modification, damage or loss.
\n\n4 . How do you manage your personal information
\n\nYou have the right to inquire, correct or supplement your information. You can do it yourself in the following ways: You can view and edit it through the setting interface on the app page. These data only exist in local files and will not be spread through the network. Users can freely delete and export data.
\n\n5 . How to update this policy \n\nOur privacy policy may change. We will not limit your rights under this policy without your express consent . For major changes, we will also provide more prominent notices (including public notices on the website or the homepage of the client or even provide you with pop-up prompts ).
\n\n6. How to contact us \n\nYou can contact us in the following ways, and we will respond to your request within 15 working days : \nWe have also set up a dedicated department for personal information protection, with the office address: Room 201 , Block B , Changtai Plaza, Lane 2889 , Jinke Road, Pudong, Shanghai ( 201203 )
\n\nThe third part of application permissions
\n\n1. When using this app , we may apply for system device permissions to collect device information and logs information, used for data analysis, and apply for storage permission to download and cache related files.
\n\n2. Phone permission: The phone permission is only used to read the mobile phone status, and it is used as a bluetooth link status. Do not collect (including but not limited to) personal information such as call content, phone numbers, etc.
\n\n3. Location permission: Location permission will only be used for the discovery and connection of Bluetooth devices and related services, and does not collect (including but not limited to) personal information location acquisition.
\n\n4. Storage permission: The storage permission will be used to store the necessary application output data. These data only exist in local files and will not be disseminated through the network. Users can freely delete and export data.
\n\n5. Use: This application is based on a series of chips produced by Hengxuan Technology , and is not suitable for all Bluetooth devices . Please pay attention to the coordination of related functions and chips when using.
\n\n6. The above permissions and sensitive permissions such as camera, photo album, storage space, GPS , etc. will not be enabled by default or forced to receive information.
\n\nHow to update this Privacy Policy We may adjust or change this Privacy Policy from time to time. Any updates to this Privacy Policy will be posted on our website by marking the update time, unless otherwise required by laws, regulations or regulatory requirements Unless there are mandatory provisions, the adjusted or changed content will take effect 7 days after notification or announcement. If you continue to use any of the services we provide or visit our related websites after the privacy policy is adjusted or changed, we believe that you have fully read, understood and accepted the revised privacy policy and are bound by it.
    </string>
    <string name="permissions_guide">In order to log the output when using the function, for follow-up analysis, we need to apply for the file storage permission of your device.</string>


    <!--  ota -->
    <string name="ota_error_config_before_start">improper configurations before starting OTA</string>
    <string name="ota_error_choose_side">selecting device side failed</string>
    <string name="ota_error_config_begin_start">config error</string>
    <string name="ota_error_percent_crc">segment CRC check failed</string>
    <string name="ota_error_whole_crc">whole crc check error</string>
    <string name="ota_error_whole_image">whole image check error</string>
    <string name="ota_error_set_user">set ota user error</string>
    <string name="ota_error_get_user_timeout">get user version timeout</string>
    <string name="ota_error_get_version_timeout">get ota version timeout</string>
    <string name="current_device">Current Selected Device</string>
    <string name="change_device">Choose Spp Device</string>
    <string name="change_device_ble">Choose Ble Device</string>
    <string name="change_device_tota_spp">Choose TOTA Spp Device</string>
    <string name="current_ota_file">Current OTA File</string>
    <string name="pick_ota_file">Choose image</string>
    <string name="pick_ota_file_left">Choose left earbud image</string>
    <string name="pick_ota_file_right">Choose right earbud image</string>
    <string name="pick_ota_file_path">Please Select Correct OTA File Path</string>
    <string name="current_ota_progress">OTA progress:</string>
    <string name="start_ota">Start OTA</string>
    <string name="ota_file_tips">Due to the change of Android storage permission, some mobile apps can only access internal files. Click Yes to switch to internal access and click Cancel to switch to external access.</string>
    <string name="ota">OTA</string>
    <string name="ota_exit_tips">OTA in progress, continue to exit?</string>
    <string name="cancel">Cancel</string>
    <string name="connecting_device">Connecting device...</string>
    <string name="ota_successfully">Successful</string>
    <string name="ota_get_version_success">get version successful</string>
    <string name="ota_set_user_success">set user successful</string>
    <string name="ota_set_upgrade_type_slow">normal</string>
    <string name="ota_set_upgrade_type_fast">fast</string>
    <string name="ota_no_breakpoint_resume">not resumed from a history interrupted ota procedure</string>
    <string name="ota_breakpoint_resume">breakpoint check pass</string>
    <string name="ota_start">ota config done successfully, start ota</string>
    <string name="ota_disconnect_success">receive 95</string>
    <string name="ota_whole_crc_success">whole crc check successful</string>
    <string name="ota_percent_crc_error_resend">segment CRC check failed, resend</string>
    <string name="ota_upgrade_type">Upgrade Type</string>
    <string name="ota_is_bth">Is BTH bin</string>

    <string name="hex_prefix">0x</string>
    <string name="ota_config_yes">Y</string>
    <string name="ota_config_no">N</string>
    <string name="ota_config_clear_user_data_tip">Clear user data?</string>
    <string name="ota_config_update_bt_addr_tip">Update BT addr?</string>
    <string name="ota_config_update_ble_name_tip">Update BLE name?</string>
    <string name="ota_config_update_ble_addr_tip">Update BLE addr?</string>
    <string name="ota_config_update_bt_name_tip">Update BT name?</string>
    <string name="ok">OK</string>
    <string name="invalid_bt_address">Invalid BT address!</string>
    <string name="invalid_ble_address">Invalid BLE Address!</string>
    <string name="invalid_bt_name">Invalid BT name!</string>
    <string name="invalid_ble_name">Invalid BLE name!</string>
    <string name="connect_device">Connect Device</string>
    <string name="disconnect_device">Disconnect Device</string>
    <string name="current_version">Firmware Version</string>
    <string name="old_ota_ways_version_tips">fetching firmware version...</string>

    <string name="left_earbud_only">left earbud only</string>
    <string name="right_earbud_only">right earbud only</string>
    <string name="both_earbuds_in_one_bin">both earbuds in one bin</string>
    <string name="both_earbuds_in_two_bins">both earbuds in two bins</string>
    <string name="steps_btn">Click on Me to view the file storage steps</string>
    <string name="steps1">Step 1</string>
    <string name="steps2">Step 2</string>
    <string name="steps3">Step 3</string>
    <string name="steps1_text">Connect the phone to the computer (the folder is not visible in the phone due to the Android access change) and select Transfer file</string>
    <string name="steps2_text">On the PC, locate the internal storage of the phone and go to the Android -> data -> com.bes.besall -> files -> bin folder</string>
    <string name="steps3_text">Copy the file to be transferred to the bin folder, and access this page again to use the file</string>

    <!--  bluetooth -->
    <string name="yes">Yes</string>
    <string name="no">No</string>
    <string name="connect_failed">Connection down</string>
    <string name="connect_success">Connecting Successful</string>
    <string name="exit_hint">Clicking the back button again will exit the application</string>

    <!--  audio dump -->
    <string name="audio_paused">Paused&#8230;</string>
    <string name="audio_playing">Playing&#8230;</string>
    <string name="audio_dump_start">Start Audio Dump</string>
    <string name="audio_dump_stop">Stop Audio Dump</string>
    <string name="audio_file_select">Choose File</string>
    <string name="audio_insert_data_form">Insert data format</string>
    <string name="audio_file_type">File type</string>

    <!--  scan -->
    <string name="scan">scan</string>
    <string name="account_input_tips">Please Enter Command</string>
    <string name="scanTips">The device whose name is green is currently connected to the app, which may be BLE or SPP. If the Service and Characteristic of the connected device are the same as those of the current function, you can skip the connect process, use the device directly.</string>

    <!--    debug tools-->
    <string name="rssi">RSSI</string>
    <string name="rssi_extend">RSSI EXTEND</string>
    <string name="audio_dump">AUDIO DUMP</string>
    <string name="log_dump">LOG DUMP</string>
    <string name="crash_dump">CRASH DUMP</string>
    <string name="customer_cmd">CUSTOM COMMAND</string>

    <!--crashdump-->
    <string name="ftp_upload">FTP UPLOAD</string>
    <string name="crash_dump_start">Start Crash Dump</string>
    <string name="crash_dump_stop">Stop Crash Dump</string>

    <!--    RSSI-->
    <string name="RSSI_STOP">RSSI STOP</string>
    <string name="NRssi_Protocol">Use Protocol (0x6306)</string>
    <string name="ORssi_Protocol">Use Protocol (0x6309)</string>

    <!--    CUSTOMER CMD-->
    <string name="EDIT">Edit</string>
    <string name="filepath">File Path</string>
    <string name="customcmd">Custom Command</string>
    <string name="importfile">Import File</string>
    <string name="exportfile">Export File</string>

    <!--    Log Dump-->
    <string name="startdownload">Start Download</string>
    <string name="downloading">downloading...</string>
    <string name="downloadcomplete">downloading complete</string>
    <string name="totalprogress">total progress:</string>

    <string name="last_device">Last Selected Device</string>
    <string name="ack_exist_no">without</string>
    <string name="ack_exist_yes">with</string>
    <string name="is_bth_yes">yes</string>
    <string name="is_bth_no">no</string>
    <string name="pleaseSelectDevice">Please Select Device</string>
    <string name="pleaseSelectOTAFile">Please Select Correct OTA File Path</string>
    <string name="pleaseCheckTheConnection">Please Check The Connection State</string>
    <string name="choose">select</string>
    <string name="findOtaFileTips">Cannot find bin file? please click me to view prompt</string>
    <string name="success">successful</string>
    <string name="back">Back</string>
    <string name="upgradeUnderWay">upgrading...</string>
    <string name="OTASuccess">Successful</string>
    <string name="OTAFail">Failed</string>
    <string name="settings">Settings</string>
    <string name="saveLog">Save Log</string>
    <string name="theLocalLog">The Local Log</string>
    <string name="appVersion">Version</string>
    <string name="delete">delete</string>
    <string name="loading">loading...</string>
    <string name="pleaseWait">please wait</string>
    <string name="send_interval">Send Interval</string>
    <string name="default_interval">Default Interval</string>
    <string name="path">path</string>

<!--    throughput-->
    <string name="title_real_value">Real Test Value</string>
    <string name="title_config_value">Config Value</string>

    <string name="totaDataEncryption">TOTA Data Encryption</string>
    <string name="use_totav2">Use TOTA V2</string>
    <string name="use_phy2m">Use PHY 2M</string>
    <string name="use_normal_connect">Use Normal Connect</string>
    <string name="DataTransmissionInterval">Data Transmission Interval</string>
    <string name="UseTheDefaultInterval">Use The Default Interval</string>

    <!--    wifi-->
    <string name="chooseWifi">Please Select Wifi</string>
    <string name="wifiName">WIFI name</string>
    <string name="wifiPassword">Password</string>
    <string name="wifiSendData">Send Data</string>
    <string name="wifiSendDataOK">Send Data Succeeded</string>
    <string name="wifiSendDataFail">Send Data Failed</string>

    <string name="stop_vibrate">Stop Vibrate</string>

    <!--    AVS LWA-->
    <string name="login_with_amazon">Login with Amazon</string>
    <string name="logout">Logout</string>
    <string name="default_message">Welcome to Login with Amazon!\nIf this is your first time logging in, you will be asked to give permission for this application to access your profile data.</string>
    <string name="login_button_content_description">"Button for authorization through Login with Amazon"</string>
    <string name="return_to_app">Return to App</string>
    <string name="wifiConnectFail">WIFI Connect Fail, Please Retry</string>
    <string name="amazon_guide">Alexa,what\'s the weather todav?\n\nAlexa,play my flsah Briefing.\n\nAlexa,set a timer for 20 minutes.\n\nAlexa,add "Dinner with Mon" to my calendar.\n\nAlexa,set an alarm for 6 a.m.</string>
    <string name="save">Save</string>
    <string name="language_select">Language Select</string>
    <string name="connecting_wifi">Connecting to WIFI...</string>
    <string name="setting_language">Setting the language for the device...</string>
    <string name="setting_language_fail">Setting fail, please retry</string>
    <string name="setting_success">Setting Success</string>
    <string name="setting_tips">Getting the current Settings...</string>

    <!--    customer dial-->
    <string name="choose_dial">Choose Dial</string>
    <string name="choose_customer_dial">Choose Customer Dial</string>
    <string name="customer_dial">Customer Dial</string>
    <string name="choose_online_dial">Online Dial</string>
    <string name="choose_picture">Picture Resource</string>
    <string name="choose_font_library">Font Library</string>
    <string name="choose_tp_firmware">TP Firmware</string>
    <string name="choose_heart_rate_firmware">Heart Rate Firmware</string>
    <string name="choose_language_packet">Language Packet</string>

    <string name="current_dial">Current Dial</string>
    <string name="choose_dfu_file">Choose dfu File</string>
    <string name="choose_old_version_file">Old Version File</string>
    <string name="start_transfer_tips">Choose Dial Or File</string>
    <string name="start_transfer_dial">Start Transfer Dial</string>
    <string name="start_transfer_dfu_file">Start Transfer dfu File</string>
    <string name="dail_background">background</string>
    <string name="dail_style">style</string>
    <string name="dail_color">color</string>
    <string name="action_sheet_tips">please choose</string>
    <string name="action_sheet_item0">camera</string>
    <string name="action_sheet_item1">album</string>
    <string name="form_square">square</string>
    <string name="form_circle">circle</string>
    <string name="crop_photo_data_name_length">height</string>
    <string name="crop_photo_data_name_diameter">diameter</string>
    <string name="crop_photo_data_name_width">width</string>
    <string name="crop_photo_save">save settings</string>
    <string name="flash_address_error">flash address error</string>
    <string name="use_incremental_upgrade">use incremental upgrade</string>
    <string name="current_dial_size">Current dial size:</string>

</resources>
