package com.bes.sdk.message;

import com.bes.sdk.utils.MessageID;

import java.util.List;

public class GestureMessage extends BaseMessage {

    private List<GestureInfo> gestureInfoList;

    public GestureMessage(List<GestureInfo> gestureInfoList) {
        this.gestureInfoList = gestureInfoList;
    }

    @Override
    public MessageID getMsgID() {
        return MessageID.GESTURE_STATUS;
    }

    @Override
    public List<GestureInfo> getMsgContent() {
        return gestureInfoList;
    }
}
