package com.besall.allbase.view.activity.chipstoollevel4.customerdial;

import android.content.Context;

import com.bes.bessdk.service.base.BesServiceConfig;
import com.bes.bessdk.service.base.BesServiceListener;

/**
 * <AUTHOR>
 * @time $ $
 */
interface ICustomerDialPresenter {

    void pickDecice(CustomerDialActivity context, int scan);

    void connectDevice(BesServiceConfig serviceConfig, BesServiceListener listener, Context context);

    void disconnect();

    void startTransfer(byte[] oldData, byte[] data, int type, int isIncremental, byte[] param);
}
