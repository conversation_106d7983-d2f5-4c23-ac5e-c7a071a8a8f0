package com.besall.allbase.view.activity.level2;


import android.content.Intent;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.widget.Button;
import android.widget.TextView;

import androidx.appcompat.widget.Toolbar;

import com.bes.bessdk.BesSdkConstants;
import com.bes.bessdk.utils.SPHelper;
import com.besall.allbase.R;
import com.besall.allbase.common.utils.ActivityUtils;
import com.besall.allbase.view.activity.chipstoollevel4.ota.DemoActivity;
import com.besall.allbase.view.activity.level3.FunctionOtaActivity;
import com.besall.allbase.view.activity.level3.FunctionToolsActivity;
import com.besall.allbase.view.activity.level3.FunctionWatchActivity;
import com.besall.allbase.view.base.BaseActivity;

/**
 * <AUTHOR>
 * @time $ $
 */
public class FunctionChosenActivity extends BaseActivity<IFunctionChosenActivity,FunctionChosenPresenter> implements IFunctionChosenActivity, View.OnClickListener {

    private static FunctionChosenActivity instance;
    private Button otaBtn;
    private Button Func_tools;
    private Button watch_tools;

    @Override
    protected FunctionChosenPresenter createPresenter() {
        return new FunctionChosenPresenter();
    }

    @Override
    protected void initBeforeSetContent() {

    }

    @Override
    protected int getContentViewId() {
        return R.layout.activity_functionchoose;
    }

    @Override
    protected void bindView() {
        otaBtn = (Button)findViewById(R.id.Func_ota);
        watch_tools = (Button)findViewById(R.id.watch_tools);
        Func_tools = (Button)findViewById(R.id.Func_tools);
//        watch_tools.setVisibility(View.GONE);
        tv_title = (TextView) findViewById(R.id.tv_title);
        mToolbar = (Toolbar) findViewById(R.id.toolbar);
    }

    @Override
    protected void initView() {
        otaBtn.setOnClickListener(this);
        watch_tools.setOnClickListener(instance);
        Func_tools.setOnClickListener(instance);

        tv_title.setText("BES TOOLS");
        mToolbar.setTitle("");
        setSupportActionBar(mToolbar);
        getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        getSupportActionBar().setHomeButtonEnabled(true);
    }

    @Override
    protected void setInstance() {
        instance = this;
    }

    @Override
    protected void removeInstance() {

    }

    public boolean onCreateOptionsMenu(Menu menu) {
        getMenuInflater().inflate(R.menu.menu_setting, menu);

        return true;
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        switch (item.getItemId()) {
            case android.R.id.home:
                finish();
                break;
            case R.id.menu_setting:
                mPresenter.goToSettingActivity(instance);
                break;
        }
        return super.onOptionsItemSelected(item);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.Func_ota:
                ActivityUtils.gotoAct(new Intent(), instance, DemoActivity.class);
                break;
            case R.id.Func_tools:
                ActivityUtils.gotoAct(new Intent(), instance, FunctionToolsActivity.class);
                break;
            case R.id.watch_tools:
                ActivityUtils.gotoAct(new Intent(), instance, FunctionWatchActivity.class);
                break;
            default:
                break;
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        //log
        SPHelper.removePreference(this, BesSdkConstants.BES_SAVE_LOG_NAME);
    }


}
