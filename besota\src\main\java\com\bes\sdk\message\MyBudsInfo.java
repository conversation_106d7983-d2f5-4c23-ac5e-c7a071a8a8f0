package com.bes.sdk.message;

import java.io.Serializable;

/**
 * My buds info
 */
public class MyBudsInfo implements Serializable {

    public static final int INVALID_STATUS = -1;
    public static final int OFF = 0;

    private int leftBeeping = INVALID_STATUS;

    private int rightBeeping = INVALID_STATUS;

    private int leftLighting = INVALID_STATUS;

    private int rightLighting = INVALID_STATUS;

    /**
     * Get left beeping statue
     *
     * @return {@link #OFF} means off, other positive digit is beep numbers, {@link #INVALID_STATUS} means invalid beeping
     */
    public int getLeftBeeping() {
        return leftBeeping;
    }

    /**
     * Set left beeping statue
     *
     * @param leftBeeping  {@link #OFF} means off, other positive digit is beep numbers, {@link #INVALID_STATUS} means invalid beeping
     */
    public void setLeftBeeping(int leftBeeping) {
        this.leftBeeping = leftBeeping;
    }

    /**
     * Get right beeping statue
     *
     * @return {@link #OFF} means off, other positive digit is beep numbers, {@link #INVALID_STATUS} means invalid beeping
     */
    public int getRightBeeping() {
        return rightBeeping;
    }

    /**
     * Set right beeping statue
     *
     * @param rightBeeping {@link #OFF} means off, other positive digit is beep numbers, {@link #INVALID_STATUS} means invalid beeping
     */
    public void setRightBeeping(int rightBeeping) {
        this.rightBeeping = rightBeeping;
    }

    /**
     * Get left lighting statue
     *
     * @return {@link #OFF} means off, other positive digit is lighting numbers, {@link #INVALID_STATUS} means invalid lighting
     */
    public int getLeftLighting() {
        return leftLighting;
    }

    /**
     * Set left lighting statue
     *
     * @param leftLighting {@link #OFF} means off, other positive digit is lighting numbers, {@link #INVALID_STATUS} means invalid lighting
     */
    public void setLeftLighting(int leftLighting) {
        this.leftLighting = leftLighting;
    }

    /**
     * Get right lighting statue
     *
     * @return  {@link #OFF} means off, other positive digit is lighting numbers, {@link #INVALID_STATUS} means invalid lighting
     */
    public int getRightLighting() {
        return rightLighting;
    }

    /**
     * Set right lighting statue
     *
     * @param rightLighting {@link #OFF} means off, other positive digit is lighting numbers, {@link #INVALID_STATUS} means invalid lighting
     */
    public void setRightLighting(int rightLighting) {
        this.rightLighting = rightLighting;
    }

    @Override
    public String toString() {
        return "MyBudsInfo{" +
                "leftBeeping=" + leftBeeping +
                ", rightBeeping=" + rightBeeping +
                ", leftLighting=" + leftLighting +
                ", rightLighting=" + rightLighting +
                '}';
    }
}
