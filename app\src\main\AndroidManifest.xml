<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.besall.allbase">

    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
    <uses-permission android:name="android.permission.BROADCAST_STICKY" />

    <!--    wifi使用-->
    <uses-permission android:name="android.permission.NEARBY_WIFI_DEVICES"
        android:usesPermissionFlags="neverForLocation" />

    <!-- Android 12以下才需要定位权限， Android 9以下官方建议申请ACCESS_COARSE_LOCATION -->
    <uses-permission
        android:name="android.permission.ACCESS_COARSE_LOCATION"
        android:maxSdkVersion="30"/>

    <!--    wifi使用-->
    <uses-permission
        android:name="android.permission.ACCESS_FINE_LOCATION"/>
    <!-- Android 12在不申请定位权限时，必须加上android:usesPermissionFlags="neverForLocation"，否则搜不到设备 -->
    <uses-permission
        android:name="android.permission.BLUETOOTH_SCAN"
        android:usesPermissionFlags="neverForLocation"
        tools:targetApi="s" />

    <uses-permission android:name="android.permission.BLUETOOTH" />
    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN"/>
    <uses-permission android:name="android.permission.BLUETOOTH_ADVERTISE" />
    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />

    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="android.permission.CAMERA" />

    <uses-permission android:name="android.hardware.usb.host" />
    <uses-feature android:name="android.hardware.usb.host"/>
    <uses-permission android:name="android.permission.RECORD_AUDIO" />

    <!--    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE"-->
<!--        tools:ignore="ScopedStorage"/>-->


    <application
        android:name=".app.MyApplication"
        android:allowBackup="true"
        android:icon="@mipmap/ic_launcher_app"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round_app"
        android:supportsRtl="true"
        android:debuggable="false"
        android:theme="@style/AppTheme"
        tools:ignore="HardcodedDebugMode">
<!--        android:requestLegacyExternalStorage="true"-->

        <activity
            android:exported="true"
            android:name="com.amazon.identity.auth.device.workflow.WorkflowActivity"
            android:allowTaskReparenting="true"
            android:launchMode="singleTask"
            android:theme="@android:style/Theme.NoDisplay">
            <intent-filter>
                <action android:name="android.intent.action.VIEW"/>

                <category android:name="android.intent.category.DEFAULT"/>
                <category android:name="android.intent.category.BROWSABLE"/>
                <!-- android:host must use the full package name found in Manifest General Attributes -->
                <data
                    android:host="${applicationId}"
                    android:scheme="amzn"/>
            </intent-filter>
        </activity>

        <activity android:name=".app.LaunchActivity"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity android:name=".view.activity.HomeActivity">

        </activity>

        <activity
            android:name=".bluetooth.scan.ScanActivity"
            android:label="@string/scan"
            android:screenOrientation="portrait" />
        <activity
            android:name=".view.activity.TestActivity"
            android:label="test"
            android:screenOrientation="portrait" />
        <activity
            android:name=".view.activity.chipstoollevel4.ota.DemoActivity"
            android:label="test"
            android:screenOrientation="portrait" />

        <activity
            android:name=".view.activity.level2.FunctionChosenActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".view.activity.level3.FunctionOtaActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".view.activity.chipstoollevel4.ota.OtaUIActivity"
            android:screenOrientation="portrait"
            android:exported="true"
            >
            <intent-filter>
                <action       android:name="android.hardware.usb.action.USB_DEVICE_ATTACHED"/>
            </intent-filter>
        </activity>

        <activity
            android:name=".view.activity.chipstoollevel4.ota.otafilelist.OtaFilelistActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".view.activity.tools.FileActivity.FilelistActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".view.activity.tools.SettingActivity.SettingActivity"
            android:screenOrientation="portrait"></activity>
        <activity
            android:name=".view.activity.tools.aboutus.AboutUsActivity"
            android:screenOrientation="portrait"></activity>

        <activity android:name=".view.activity.tools.AudioListActivity.AudioListActivity" />

        <activity android:name=".view.activity.tools.FileActivity.LogFilelistActivity" />

        <activity android:name=".view.activity.tools.FileActivity.CrashFilelistActivity" />

        <activity android:name=".view.activity.tools.FileActivity.CustomCmdFilelistActivity" />


        <activity android:name=".view.activity.chipstoollevel4.commandset.CommandSetActivity"/>
        <activity android:name=".view.activity.chipstoollevel4.checkcrc.CheckCrcActivity"/>
        <activity android:name=".view.activity.chipstoollevel4.customercmd.CustomCmdActivity"/>
        <activity android:name=".view.activity.level3.FunctionToolsActivity"/>
        <activity android:name=".view.activity.level3.FunctionWatchActivity"/>

        <activity android:name=".view.activity.chipstoollevel4.findmy.FindMyActivity"/>

        <activity android:name=".view.activity.chipstoollevel4.customerdial.CustomerDialActivity"/>

        <activity android:name=".view.activity.chipstoollevel4.customerdial.makedial.MakeDialActivity"/>

        <activity android:name=".view.activity.chipstoollevel4.customerdial.makedial.CropPhotoActivity"/>

        <provider android:name="androidx.core.content.FileProvider" android:authorities="com.makedial.FileProvider" android:exported="false" android:grantUriPermissions="true"> <meta-data android:name="android.support.FILE_PROVIDER_PATHS" android:resource="@xml/path" /> </provider>

    </application>

</manifest>