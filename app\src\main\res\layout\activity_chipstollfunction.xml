<?xml version="1.0" encoding="utf-8"?>
<!--<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"-->
<!--    xmlns:tools="http://schemas.android.com/tools"-->
<!--    android:layout_width="match_parent"-->
<!--    android:layout_height="match_parent"-->
<!--    android:orientation="vertical"-->
<!--    android:background="@drawable/base_bg"-->
<!--    >-->
    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:background="@mipmap/bes_bg6"

        >

    <include
        android:id="@+id/tool"
        layout="@layout/toolbar"
        />
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="30dp"
        android:layout_marginBottom="50dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dip"
                android:background="#FFE1E6EB"
                />

            <Button
                android:id="@+id/check_crc"
                android:layout_width="match_parent"
                android:layout_height="58dp"
                android:gravity="left|center_vertical"
                android:padding="20dp"
                android:drawableLeft="@drawable/chips_tools_icon_customer"
                android:drawableRight="@drawable/home_icon_arrow"
                android:background="@drawable/rectangle_longbtn"
                android:textColor="@color/ff2c4662"
                android:text="    CHECK CRC"
                android:textAllCaps="false"
                />

            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dip"
                android:background="#FFE1E6EB"
                android:layout_marginLeft="20dp"
                />

            <Button
                android:id="@+id/custom_command"
                android:layout_width="match_parent"
                android:layout_height="58dp"
                android:gravity="left|center_vertical"
                android:padding="20dp"
                android:drawableLeft="@drawable/chips_tools_icon_customer"
                android:drawableRight="@drawable/home_icon_arrow"
                android:background="@drawable/rectangle_longbtn"
                android:textColor="@color/ff2c4662"
                android:text="    CUSTOMER CMD"
                android:textAllCaps="false"
                />

            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dip"
                android:background="#FFE1E6EB"
                android:layout_marginLeft="20dp"
                />

            <Button
                android:id="@+id/command_set"
                android:layout_width="match_parent"
                android:layout_height="58dp"
                android:gravity="left|center_vertical"
                android:padding="20dp"
                android:drawableLeft="@drawable/chips_tools_icon_customer"
                android:drawableRight="@drawable/home_icon_arrow"
                android:background="@drawable/rectangle_longbtn"
                android:textColor="@color/ff2c4662"
                android:text="    COMMAND SET"
                android:textAllCaps="false"
                />

            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dip"
                android:background="#FFE1E6EB"

                />
        </LinearLayout>

    </ScrollView>


    </LinearLayout>

<!--</ScrollView>-->