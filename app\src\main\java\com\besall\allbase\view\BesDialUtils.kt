package com.besall.allbase.view

import android.content.Context
import android.graphics.*
import android.util.Log
import com.bes.sdk.core.OtaLog
import com.besall.allbase.app.MyApplication
import com.blankj.utilcode.util.ConvertUtils
import java.io.File
import java.io.FileOutputStream
import java.text.SimpleDateFormat
import java.util.*

/**
 *
 * <AUTHOR>
 * @date 2023/6/29 17:36
 */

object BesDialUtils {
    //把图片转成256色png
    fun magickImageCommande(key: String, bitmap: Bitmap, context: Context): Bitmap {
        val srcFile = File(context.getExternalFilesDir("magick"), key)
        val prewFileOutputStream = FileOutputStream(srcFile)
        bitmap.compress(Bitmap.CompressFormat.PNG, 100, prewFileOutputStream)
        prewFileOutputStream.close()
        val srcSize = ConvertUtils.byte2FitMemorySize(srcFile.length())
        //File(context.getExternalFilesDir("magick"), key) //
        val outFile = File(context.getExternalFilesDir("magick"), "out_${key}")
        BesOtaUtils.magickImageCommande(srcFile.absolutePath, outFile.absolutePath)
        if (outFile.isFile) {
            OtaLog.logI(
                "压缩率 src ${srcSize}  out ${
                    ConvertUtils.byte2FitMemorySize(outFile.length())
                }"
            )
            val options = BitmapFactory.Options()
            options.inPreferredConfig = Bitmap.Config.ARGB_8888
            return BitmapFactory.decodeFile(outFile.absolutePath, options)
        } else {
            OtaLog.logE("magickImageCommande error ${key}")
            return bitmap
        }
    }


    fun buildDialDfu(id: Int, style: Int, color: Int, thumb: Bitmap, bgBitmap: Bitmap): ByteArray {
        val dialStyle = when (style) {
            0 -> 1  //文本1
            1 -> {
                if (Color.BLACK == color) {
                    3 //黑指针
                } else {
                    2 //白指针
                }
            }
            else -> {
                1
            }
        }

        val formatter = SimpleDateFormat("yyyy-MM-dd HH:mm:ss")
        return BesOtaUtils.buildDialDfu(BesOtaUtils.DfuDialInfo().apply {
            this.id = id
            this.style = dialStyle
            this.color = color
            this.versionName = "ver_${formatter.format(Date())}"
        }, ArrayList<BesOtaUtils.BinInfo>().apply {
            add(BesOtaUtils.BinInfo().apply {
                val key = "thumb_${id}_${dialStyle}_${color.toHexString()}.png"
                this.binBytes = Index8ImageUtils.getIndex8(magickImageCommande(key,thumb,
                    MyApplication.getContext()))
                this.name = "img_dial_thumbnail.png"
                this.crc16 = CrcUtil.crc_16_CCITT_False(this.binBytes)
                this.size = this.binBytes!!.size
            })
            add(BesOtaUtils.BinInfo().apply {
                val key = "bg_${id}_${dialStyle}_${color.toHexString()}.png"
                this.binBytes = Index8ImageUtils.getIndex8(magickImageCommande(key,bgBitmap,MyApplication.getContext()))
                this.name = "img_dial_background.png"
                this.crc16 = CrcUtil.crc_16_CCITT_False(this.binBytes)
                this.size = this.binBytes!!.size
            })
        })

    }
    fun Int.toHexString(): String = Integer.toHexString(this)

    fun test() {

    }

    fun createThumbnail(
        bgBitmap: Bitmap, thumbWidth: Int, thumbHeight: Int,style: Int,color: Int
    ): Bitmap {
        var dWidth = thumbWidth
        if (thumbWidth == 0) {
            dWidth = 256
        }
        var dHeight = thumbHeight
        if (thumbHeight == 0) {
            dHeight = 288
        }
        return  drawStyle1(bgBitmap,dWidth,dHeight,color)

    }

    //文本样式
    private fun drawStyle1(bgBitmap: Bitmap, thumbWidth: Int, thumbHeight: Int, color: Int):Bitmap {

        val thumbnailBitmap =
            Bitmap.createBitmap(thumbWidth, thumbHeight, Bitmap.Config.ARGB_8888)
                .apply {
                    val canvas = Canvas(this)
                    val rect = Rect(0, 0, thumbWidth, thumbHeight)
                    val rectF = RectF(0f, 0f, thumbWidth.toFloat(), thumbHeight.toFloat())
                    val path = Path()
                    path.addRoundRect(rectF, 60f, 60f, Path.Direction.CCW)
                    canvas.clipPath(path)
                    canvas.drawBitmap(
                        bgBitmap,
                        Rect(0, 0, bgBitmap.width, bgBitmap.height),
                        rect,
                        Paint()
                    )

                }

        Log.d(TAG, "drawStyle1: ")
        return thumbnailBitmap
    }

    const val TAG = "sinyi"


}