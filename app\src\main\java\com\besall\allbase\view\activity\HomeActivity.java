package com.besall.allbase.view.activity;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.content.pm.PackageManager;
import android.os.Build;
import android.view.Menu;
import android.view.View;
import android.widget.Button;
import android.widget.TextView;

import com.bes.bessdk.BesSdkConstants;
import com.bes.bessdk.utils.SPHelper;
import com.besall.allbase.R;
import com.besall.allbase.common.manager.PermissionManager;
import com.besall.allbase.common.utils.FileUtils;
import com.besall.allbase.common.utils.ActivityUtils;
import com.besall.allbase.view.activity.chipstoollevel4.ota.DemoActivity;
import com.besall.allbase.view.activity.level2.FunctionChosenActivity;
import com.besall.allbase.view.activity.tools.confirmdialog.ConfirmDialog;
import com.besall.allbase.view.activity.tools.confirmdialog.ConfirmDialoglistener;
import com.besall.allbase.view.base.BaseActivity;


public class HomeActivity extends BaseActivity<IHomeActivity, HomePresenter> implements IHomeActivity, View.OnClickListener {

    private final String TAG = getClass().getSimpleName();
    private final String AGREE_KEY = "Bes_Agree_Key";
    private SharedPreferences preferences;
    private SharedPreferences.Editor editor;

    private static HomeActivity instance;
    private Button entry_btn;
    private Button testOTABtn;
    private View agree_view;
    private TextView agreeTV;
    private Button privacy_policy;
    private Button agree;
    private Button disagree;

    private TextView version_text;

    @Override
    protected HomePresenter createPresenter() {
        return new HomePresenter();
    }

    @Override
    protected void initBeforeSetContent() {
        preferences = getSharedPreferences("AGREE_KEY", 0);
        editor = preferences.edit();
    }

    @Override
    protected int getContentViewId() {
        return R.layout.activity_main;
    }

    @Override
    protected void bindView() {
        entry_btn = (Button) findViewById(R.id.entry_btn);
        testOTABtn = (Button) findViewById(R.id.testOTABtn);
        agree_view = (View) findViewById(R.id.agree_view);
        privacy_policy = (Button) findViewById(R.id.privacy_policy);
        agree = (Button) findViewById(R.id.agree);
        disagree = (Button) findViewById(R.id.disagree);
        agreeTV = (TextView) findViewById(R.id.agreeTV);
        version_text = (TextView) findViewById(R.id.version_text);
    }

    @SuppressLint("ResourceAsColor")
    @Override
    protected void initView() {
//        Intent intent = new Intent();
//        ActivityUtils.gotoAct(intent, instance, TestActivity.class);
        entry_btn.setOnClickListener(instance);
        testOTABtn.setOnClickListener(instance);
        privacy_policy.setOnClickListener(instance);
        agree.setOnClickListener(instance);
        disagree.setOnClickListener(instance);

        agreeTV.setText(getString(R.string.agreement));

        version_text.setText(getString(R.string.appVersion) + ": " + getVersionName(instance));

        boolean show = preferences.getBoolean(AGREE_KEY, true);
        if (show) {
            agree_view.setVisibility(View.VISIBLE);
        } else {
            agree_view.setVisibility(View.GONE);
        }

        //init blueToothConnect
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            SPHelper.putPreference(instance, BesSdkConstants.BES_USE_NORMAL_CONNECT, false);
        } else {
            SPHelper.putPreference(instance, BesSdkConstants.BES_USE_NORMAL_CONNECT, true);
        }

        //init bin path
        new FileUtils(instance).initBinFile();

//        try {
//            new ImageMagic().MagickImageCommande("path","outPath");
//        } catch (Exception e) {
//            throw new RuntimeException(e);
//        }

    }

    public static String getVersionName(Context mContext) {
        String versionName = "";
        try {
            //获取软件版本号，对应AndroidManifest.xml下android:versionCode
            versionName = mContext.getPackageManager().
                    getPackageInfo(mContext.getPackageName(), 0).versionName;
        } catch (PackageManager.NameNotFoundException e) {
            e.printStackTrace();
        }
        return versionName;
    }

    @Override
    protected void setInstance() {
        instance = this;
    }

    @Override
    protected void removeInstance() {

    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.entry_btn:
                ActivityUtils.gotoAct(new Intent(), instance, FunctionChosenActivity.class);
                break;
            case R.id.testOTABtn:
                ActivityUtils.gotoAct(new Intent(), instance, DemoActivity.class);
                break;
            case R.id.privacy_policy:
                agree_view.setVisibility(View.VISIBLE);
                break;
            case R.id.agree:
                agree_view.setVisibility(View.GONE);
                editor.putBoolean(AGREE_KEY, false);
                editor.commit();

                showConfirmDialog(getString(R.string.permissions_guide));
                break;
            case R.id.disagree:
                editor.putBoolean(AGREE_KEY, true);
                editor.commit();
                finish();
                break;
            default:
                break;
        }
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        getMenuInflater().inflate(R.menu.main, menu);

        return true;
    }


    private void showConfirmDialog(String msg) {
        ConfirmDialog confirmDialog = new ConfirmDialog(instance, msg, new ConfirmDialoglistener() {
            @Override
            public void confirmYes() {
                PermissionManager.getInstance().requestPermissions(HomeActivity.this, null, PermissionManager.Permission.Storage.WRITE_EXTERNAL_STORAGE);
            }

            @Override
            public void confirmNo() {

            }
        });

        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                confirmDialog.show();
            }
        });
    }

}
