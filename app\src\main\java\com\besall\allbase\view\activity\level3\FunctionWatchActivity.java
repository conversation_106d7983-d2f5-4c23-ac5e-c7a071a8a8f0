package com.besall.allbase.view.activity.level3;


import android.content.Intent;
import android.view.MenuItem;
import android.view.View;
import android.widget.Button;
import android.widget.TextView;

import androidx.appcompat.widget.Toolbar;

import com.besall.allbase.R;
import com.besall.allbase.common.utils.ActivityUtils;
import com.besall.allbase.view.activity.chipstoollevel4.customerdial.CustomerDialActivity;
import com.besall.allbase.view.activity.chipstoollevel4.findmy.FindMyActivity;
import com.besall.allbase.view.base.BaseActivity;

/**
 * <AUTHOR>
 * @time $ $
 */
public class FunctionWatchActivity extends BaseActivity<IFunctionWatchActivity, FunctionWatchPresenter> implements IFunctionWatchActivity, View.OnClickListener {

    private static FunctionWatchActivity instance;

    private Button find_my;

    private Button watch_dial;

    @Override
    protected FunctionWatchPresenter createPresenter() {
        return new FunctionWatchPresenter();
    }

    @Override
    protected void initBeforeSetContent() {

    }

    @Override
    protected int getContentViewId() {
        return R.layout.activity_watchfunction;
    }

    @Override
    protected void bindView() {
        find_my = (Button)findViewById(R.id.find_my);
        watch_dial = (Button)findViewById(R.id.watch_dial);

        tv_title = (TextView) findViewById(R.id.tv_title);
        mToolbar = (Toolbar) findViewById(R.id.toolbar);

    }

    @Override
    protected void initView() {
        find_my.setOnClickListener(instance);
        tv_title.setText("WATCH TOOLS");
        mToolbar.setTitle("");
        setSupportActionBar(mToolbar);
        getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        getSupportActionBar().setHomeButtonEnabled(true);
        watch_dial.setOnClickListener(instance);

    }

    @Override
    protected void setInstance() {
        instance = this;
    }

    @Override
    protected void removeInstance() {
        instance = null;
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        switch (item.getItemId()) {
            case android.R.id.home:
                finish();
                break;
        }
        return super.onOptionsItemSelected(item);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.find_my:
                ActivityUtils.gotoAct(new Intent(), instance, FindMyActivity.class);
                break;
            case R.id.watch_dial:
                ActivityUtils.gotoAct(new Intent(), instance, CustomerDialActivity.class);

                break;
            default:
                break;
        }
    }
}
