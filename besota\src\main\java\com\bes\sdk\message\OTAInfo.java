package com.bes.sdk.message;

import androidx.annotation.NonNull;

import com.bes.sdk.utils.OTAChannel;
import com.bes.sdk.utils.OTAStatus;

import java.io.Serializable;

/**
 * Anc status definition
 */
public class OTAInfo implements Serializable
{
    /**
     * OTA channel
     */
    private OTAChannel otaChannel;

    /**
     * OTA status.
     */
    private OTAStatus otaStatus;

    public @NonNull OTAChannel getOtaChannel() {
        return otaChannel;
    }

    public void setOtaChannel(OTAChannel otaChannel) {
        this.otaChannel = otaChannel;
    }

    public OTAStatus getOtaStatus() {
        return otaStatus;
    }

    public void setOtaStatus(OTAStatus otaStatus) {
        this.otaStatus = otaStatus;
    }

    @Override
    public String toString() {
        return "OTAInfo{" +
                "otaChannel=" + otaChannel +
                ", otaStatus=" + otaStatus +
                '}';
    }
}
