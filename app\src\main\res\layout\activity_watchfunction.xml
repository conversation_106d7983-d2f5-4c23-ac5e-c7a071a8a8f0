<?xml version="1.0" encoding="utf-8"?>
<!--<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"-->
<!--    xmlns:tools="http://schemas.android.com/tools"-->
<!--    android:layout_width="match_parent"-->
<!--    android:layout_height="match_parent"-->
<!--    android:orientation="vertical"-->
<!--    android:background="@drawable/base_bg"-->
<!--    >-->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@mipmap/bes_bg6"

    >

    <include
        android:id="@+id/tool"
        layout="@layout/toolbar"
        />

    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dip"
        android:background="#FFE1E6EB"
        android:layout_marginTop="30dp"
        />
    <Button
        android:id="@+id/find_my"
        android:layout_width="match_parent"
        android:layout_height="58dp"
        android:gravity="left|center_vertical"
        android:padding="20dp"
        android:background="@drawable/rectangle_longbtn"
        android:drawableLeft="@drawable/chips_tools_icon_rssi"
        android:drawableRight="@drawable/home_icon_arrow"
        android:text="    Find My"
        android:textColor="@color/ff2c4662"
        />

    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dip"
        android:background="#FFE1E6EB"

        />

    <Button
        android:id="@+id/watch_dial"
        android:layout_width="match_parent"
        android:layout_height="58dp"
        android:gravity="left|center_vertical"
        android:padding="20dp"
        android:background="@drawable/rectangle_longbtn"
        android:drawableLeft="@drawable/chips_tools_icon_customer"
        android:drawableRight="@drawable/home_icon_arrow"
        android:text="    CUSTOMER DIAL"
        android:textColor="@color/ff2c4662"
        />

    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dip"
        android:background="#FFE1E6EB"

        />
</LinearLayout>
