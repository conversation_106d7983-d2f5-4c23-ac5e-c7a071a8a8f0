package com.bes.bessdk.service.customcmd;

import static com.bes.bessdk.service.customcmd.CustomCmdConstants.OP_TOTA_SET_CUSTOMER_CMD;

import android.content.Context;
import android.util.Log;

import com.bes.bessdk.utils.ArrayUtil;
import com.bes.bessdk.utils.CmdInfo;

public class
CustomCmdCMD {
    static String TAG = "CustomCmdCMD";

    static String startTime = "";
    public static String rssiinfo = "";

    public static byte[] bytes = new byte[256];
    public static byte[] showinfo = new byte[30];
    public static byte[] cmdsend = new byte[260];

    public static void cmdchange(String data) {
        String name = data;
        String name16 = ArrayUtil.str2HexStr(name);
        byte[] namebytes = ArrayUtil.toBytes(name16);
        bytes = new byte[256];
        for (int i = 0; i < namebytes.length; i++) {
            bytes[i] = namebytes[i];
        }
    }

    public static byte[] SendCustomCmd(String cmd, int i) {
        showinfo = new byte[30];
        bytes = new byte[256];
        if (i == 0) {
            cmdchange(cmd);
        } else if (i == 1) {
            byte[] byte16 = ArrayUtil.toBytes(cmd);
            for (int j = 0; j < byte16.length; j++) {
                bytes[j] = byte16[j];
            }
        }
        CmdInfo curcmd = new CmdInfo(OP_TOTA_SET_CUSTOMER_CMD, bytes);
        System.arraycopy(curcmd.toBytes(), 0, showinfo, 0, 30);
        System.arraycopy(curcmd.toBytes(),0, cmdsend, 0, 260);
        Log.i(TAG, "SendCustomCmd: ---" + ArrayUtil.toHex(cmdsend));
//        showcmd();
        return cmdsend;
    }

//    public static String showcmd() {
//        return ArrayUtil.toHex(showinfo);
//    }

//    public static byte[] SendCustomCmd(byte[] cmd) {
//        Log.i(TAG, "CMDCmd: " + ArrayUtil.toHex(cmd));
//        byte[] CmdinfoType = new byte[260];
//        System.arraycopy(cmd,0,CmdinfoType,0,cmd.length);
//        CmdInfo CustomCmdinfo = new CmdInfo(CustomCmdConstants.OP_TOTA_SET_CUSTOMER_CMD, CmdinfoType);
//        return cmd;
//    }

    public static String receiveData(byte[] data, Context context) {

       return "";
    }

}