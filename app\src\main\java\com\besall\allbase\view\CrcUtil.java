package com.besall.allbase.view;

/**
 * <AUTHOR>
 * @date 2023/6/28 17:35
 */

public class CrcUtil {
    /**
     * CRC-16/CCITT-FALSE x16+x12+x5+1 算法
     * <p>
     * info
     * Name:CRC-16/CCITT-FAI
     * Width:16
     * Poly:0x1021
     * Init:0x0000
     * RefIn:False
     * RefOut:False
     * XorOut:0x0000
     *
     * @param bytes 原始数据
     * @return 十进制的crc结果
     */
    public static int crc_16_CCITT_False(byte[] bytes) {
        int crc = 0x0000; // initial value
        int polynomial = 0x1021; // poly value
        for (int index = 0; index < bytes.length; index++) {
            byte b = bytes[index];
            for (int i = 0; i < 8; i++) {
                boolean bit = ((b >> (7 - i) & 1) == 1);
                boolean c15 = ((crc >> 15 & 1) == 1);
                crc <<= 1;
                if (c15 ^ bit)
                    crc ^= polynomial;
            }
        }
        crc &= 0xffff;
        //输出String字样的16进制
        String strCrc = Integer.toHexString(crc).toUpperCase();
        System.out.println(strCrc);
        return crc;
    }

}
