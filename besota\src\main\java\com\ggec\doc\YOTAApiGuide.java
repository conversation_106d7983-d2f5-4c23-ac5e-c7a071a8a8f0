package com.ggec.doc;

/**
 * YOTA SDK 调用指南
 * ===============
 * 
 * 本文档提供了YOTA SDK的基本使用方法和示例代码，帮助开发者快速集成OTA升级功能。
 * 
 * 1. 简介
 * -------
 * YOTA SDK提供了蓝牙设备OTA升级的完整解决方案，支持固件升级、进度监控和状态回调等功能。
 * 主要通过{@link com.ggec.yotasdk.YOTAApi}接口实现各种功能。
 * 
 * 2. 快速开始
 * -----------
 * 
 * 2.1 初始化SDK
 * 
 * <pre>
 * // 获取YOTA SDK实例
 * YOTAApi yotaApi = YOTAManager.getInstance();
 * </pre>
 * 
 * 2.2 开始升级
 * 
 * <pre>
 * // 设备参数
 * HmDevice device = ...; // 通过蓝牙搜索获取的设备对象
 * String filePath = "/sdcard/firmware.bin"; // 升级固件路径
 * 
 * // 开始升级
 * boolean result = yotaApi.startUpgrade(device, filePath);
 * if (result) {
 *     Log.d("YOTA", "升级流程已启动");
 * } else {
 *     Log.e("YOTA", "启动升级失败");
 * }
 * </pre>
 * 
 * 3. 监听升级进度
 * ---------------
 * 
 * <pre>
 * yotaApi.setProgressListener(new YOTAApi.ProgressListener() {
 *     @Override
 *     public void onProgressChanged(float progress) {
 *         // 进度范围0-100
 *         Log.d("YOTA", "当前升级进度: " + progress + "%");
 *         // 更新UI进度条
 *         progressBar.setProgress((int)progress);
 *     }
 * });
 * </pre>
 * 
 * 4. 监听状态变化
 * ---------------
 * 
 * <pre>
 * yotaApi.setStatusListener(new YOTAApi.StatusListener() {
 *     @Override
 *     public void onStatusChanged(OTAStatus status) {
 *         switch (status) {
 *             case CONNECTING:
 *                 Log.d("YOTA", "正在连接设备...");
 *                 break;
 *             case TRANSFERRING:
 *                 Log.d("YOTA", "正在传输数据...");
 *                 break;
 *             case VERIFYING:
 *                 Log.d("YOTA", "正在验证固件...");
 *                 break;
 *             // 处理其他状态...
 *         }
 *     }
 *     
 *     @Override
 *     public void onError(int errorCode, String message) {
 *         Log.e("YOTA", "升级出错: " + message + " (错误码: " + errorCode + ")");
 *         // 显示错误信息
 *         showErrorDialog(message);
 *     }
 *     
 *     @Override
 *     public void onSuccess() {
 *         Log.d("YOTA", "升级成功完成!");
 *         // 显示升级成功提示
 *         showSuccessDialog();
 *     }
 * });
 * </pre>
 * 
 * 5. 取消升级
 * -----------
 * 
 * <pre>
 * // 用户取消或需要中断升级时调用
 * boolean cancelResult = yotaApi.cancelUpgrade();
 * if (cancelResult) {
 *     Log.d("YOTA", "升级已取消");
 * } else {
 *     Log.e("YOTA", "取消升级失败");
 * }
 * </pre>
 * 
 * 6. 完整示例
 * -----------
 * 
 * <pre>
 * // 1. 获取SDK实例
 * YOTAApi yotaApi = YOTAManager.getInstance();
 * 
 * // 2. 设置监听器
 * yotaApi.setProgressListener(progressListener);
 * yotaApi.setStatusListener(statusListener);
 * 
 * // 3. 启动升级
 * boolean result = yotaApi.startUpgrade(hmDevice, firmwarePath);
 * 
 * // 4. 处理用户取消场景
 * cancelButton.setOnClickListener(v -> {
 *     yotaApi.cancelUpgrade();
 * });
 * </pre>
 * 
 * 7. 注意事项
 * -----------
 * 
 * - 确保在调用startUpgrade前已经设置好监听器
 * - 升级过程中避免应用退出到后台，可能导致升级中断
 * - 确保设备电量充足，避免在升级过程中关机
 * - 升级文件必须是有效的固件包，否则可能导致设备无法启动
 * - 升级过程中保持设备与手机距离较近，避免蓝牙断开
 */
public class YOTAApiGuide {
    // 此类仅作为文档使用，不包含实际实现
} 