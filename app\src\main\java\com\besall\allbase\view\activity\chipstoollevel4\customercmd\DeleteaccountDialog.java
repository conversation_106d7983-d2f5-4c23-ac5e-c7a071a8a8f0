package com.besall.allbase.view.activity.chipstoollevel4.customercmd;

import android.app.Dialog;
import android.content.Context;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.widget.Button;
import android.widget.TextView;

import com.besall.allbase.R;
import com.besall.allbase.common.utils.ResourceUtil;


public class DeleteaccountDialog extends Dialog {
    private Context context;
    private Button cancel;
    private Button confirm;
    private TextView accounttext;
    private Deleteaccountlistener deleteaccountlistener;
    private String account;


    public DeleteaccountDialog(Context context, String account, Deleteaccountlistener
            deleteaccountlistener) {
        super(context);
        this.context = context;
        this.account = account;
        this.deleteaccountlistener = deleteaccountlistener;

    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        getWindow().setBackgroundDrawableResource(android.R.color.transparent);
        getWindow().requestFeature(Window.FEATURE_NO_TITLE);
        LayoutInflater inflater = (LayoutInflater) context
                .getSystemService(Context.LAYOUT_INFLATER_SERVICE);
        View layout = inflater.inflate(ResourceUtil.getLayoutId(context,
                "confirm_dialog"), null);
        setContentView(layout);
        initview();
    }

    private void initview() {
        cancel = (Button)findViewById(R.id.dialog_cancel);
        confirm = (Button)findViewById(R.id.dialog_confirm);
        accounttext = findViewById(ResourceUtil.getId(context, "dialog_text"));
        accounttext.setText("DELETE CMD [" + account + "]");
        //删除账号取消
        cancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                DeleteaccountDialog.this.dismiss();
            }
        });
        //确定删除账号
        confirm.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                DeleteaccountDialog.this.dismiss();
                deleteaccountlistener.deleteaccountsuccess();
            }
        });
    }


}
