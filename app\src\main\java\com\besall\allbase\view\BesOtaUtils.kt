package com.besall.allbase.view

import android.util.Log
import com.bes.bessdk.utils.FileUtils
import com.bes.d3dsdk.ImageMagic
import com.bes.sdk.core.BesOtaEntity
import com.bes.sdk.core.OtaLog
import com.blankj.utilcode.util.ZipUtils
import java.io.File

/**
 *
 * 处理bes OTA的分析，解析
 * <AUTHOR>
 * @date 2023/6/25 18:01
 */

object BesOtaUtils {

    data class FwVersionInfo(val ver:String="")
    fun buildBesOtaEntity(
        versionInfo: FwVersionInfo,
        filePath: String
    ): BesOtaEntity {
        val otaEntity = BesOtaEntity()
        if (filePath.endsWith(".zip")) {
            val otaZipFile: File? =File(filePath)
            val otaFileParentDir = otaZipFile?.parent + "/bes"
            FileUtils.deleteDir(otaFileParentDir)
            FileUtils.createOrExistsDir(otaFileParentDir)
            ZipUtils.unzipFile(filePath, otaFileParentDir)
            val otaFiles = FileUtils.listFilesInDir(otaFileParentDir)
            if (otaFiles == null || otaFiles.isEmpty()) return otaEntity
            val otaDataList = mutableListOf<BesOtaEntity.OtaItem>()
            for (file in otaFiles) {
                if (!file.exists()) continue
                if (file.isFile && file.name.endsWith(".bin", true) && !file.name.startsWith(".")) {
                    val fileType = getFileTypeByName(file.name)
                    if (fileType >= 0) {
                        otaDataList.add(BesOtaEntity.OtaItem().apply {
                            name = file.name
                            type = fileType
                            bytes = file.readBytes()
                        })
                    } else {
                        OtaLog.logE("未识别文件类型 $file.name")
                    }
                }
            }
            otaEntity.otaDataList = otaDataList
        } else {
            //按DFU的格式进行解析
            val dfuInfo = readDfu(File(filePath))
            if (dfuInfo != null) {
                OtaLog.logI("dfuInfo $dfuInfo")
                val otaDataList = mutableListOf<BesOtaEntity.OtaItem>()
                dfuInfo.binInfos?.let {
                    it.forEach { bin ->
                        val fileType = getFileTypeByName(bin.name)
                        if (fileType >= 0) {  //这里还要判断版本号，剔除一样的版本

                            otaDataList.add(BesOtaEntity.OtaItem().apply {
                                name = bin.name
                                type = fileType
                                bytes = bin.binBytes
                            })
                        } else {
                            OtaLog.logE("未识别文件类型 ${bin.name}")
                        }
                    }
                }
            } else {
                OtaLog.logE("dfuInfo 解析设备失败")
            }
        }
        return otaEntity
    }
    private fun getFileTypeByName(name: String): Int {
        var type = -1
        //还差1 和 6 hr
        if (name.startsWith("bes_band", true) || name.startsWith("app", true)) {
            type = 0
        } else if (name.startsWith("font", true)) {
            type = 4

        } else if (name.startsWith("image", true)) {
            type = 3

        } else if (name.contains("boot", true)) {
            type = 8

        } else if (name.contains("tp", true)) {
            type = 5

        } else if (name.contains("dial", true)) {
            type = 2

        } else if (name.contains("language", true)) {
            type = 7
        }
        return type
    }

    fun magickImageCommande(imagePath: String, outImagePath: String) {
        ImageMagic.MagickImageCommande(imagePath, outImagePath);
    }


    const val HEAD_MAGIC = 0x1234abcd
    const val FOOTER_MAGIC = 0xa5a5a5a5

    const val HEAD_INFO_LENGTH = 256
    const val HEAD_SUBFILE_INFO_LENGTH = 76

    const val HEAD_FILE_NAME_MAX = 48
    const val HEAD_AUTHOR_MAX = 16
    const val HEAD_VERSION_MAX = 24
    const val HEAD_RESERVE_LENGTH = 20
    const val HEAD_SUBFILE_NAME_MAX = 64
    const val FOOTER_INFO_LENGTH = 128
    const val FOOTER_RESERVE_LENGTH = 100


    private fun readDfu(dfuFile: File): DfuInfo? {
        val fileBytes = dfuFile.readBytes()
        if (fileBytes.size <= 128 * 2) return null
        var offset = 0
        val magic = fileBytes.readUint32(0)
        offset += 4

        if (magic == HEAD_MAGIC) {
            Log.d("TAG", "magic校验成功")
        }
        val headSize = fileBytes.readUint32(offset)
        offset += 4

        val footerSize = fileBytes.readUint32(offset)
        offset += 4

        val name = fileBytes.toUtf8String(offset, HEAD_FILE_NAME_MAX)
        offset += HEAD_FILE_NAME_MAX

        val author = fileBytes.toUtf8String(offset, HEAD_AUTHOR_MAX)
        offset += HEAD_AUTHOR_MAX

        val version = fileBytes.toUtf8String(offset, HEAD_VERSION_MAX)
        offset += HEAD_VERSION_MAX

        val subCode = fileBytes.readUint32(offset)
        offset += 4

        val crc16 = fileBytes.readUint32(offset)
        offset += 4

        val length = fileBytes.readUint32(offset)
        offset += 4

        val fileNum = fileBytes.readUint32(offset)
        offset += 4

        val reserve = fileBytes.readBytes(offset, HEAD_RESERVE_LENGTH)
        //偏移到头
        offset = HEAD_INFO_LENGTH
        val binInfos = mutableListOf<BinInfo>()
        if (fileNum > 0) {
            var num = fileNum
            while (num > 0) {
                binInfos.add(BinInfo().apply {
                    this.crc16 = fileBytes.readUint32(offset)
                    offset += 4
                    this.addr = fileBytes.readUint32(offset)
                    offset += 4
                    this.size = fileBytes.readUint32(offset)
                    offset += 4
                    this.name = fileBytes.toUtf8String(offset, HEAD_SUBFILE_NAME_MAX)
                    offset += HEAD_SUBFILE_NAME_MAX
                })
                num--
            }
            for (binInfo in binInfos) {
                binInfo.binBytes = fileBytes.readBytes(binInfo.addr, binInfo.size)
                val crc16 = CrcUtil.crc_16_CCITT_False(binInfo.binBytes)
                if (crc16 != binInfo.crc16) {
                    Log.e("sinyi", "readDfu: crc16 不匹配 $crc16  $binInfo")
                }
            }
        }
        val footerBytes =
            fileBytes.readBytes(fileBytes.size - FOOTER_INFO_LENGTH, FOOTER_INFO_LENGTH)
        val footerMagic = footerBytes.readUint32(0)
        val footerVersion = footerBytes.toUtf8String(4, HEAD_VERSION_MAX)
        if (footerMagic == FOOTER_INFO_LENGTH) {
            Log.d("sinyi", "footerMagic: 校验成功")
        }

        val dfuInfo = DfuInfo()
        dfuInfo.name = name
        dfuInfo.author = author
        dfuInfo.version = version
        dfuInfo.subCode = subCode
        dfuInfo.crc16 = crc16
        dfuInfo.length = length
        dfuInfo.reserve = reserve
        dfuInfo.footerVersion = footerVersion
        dfuInfo.binInfos = binInfos
        return dfuInfo
    }


    //-生成表盘dfu的bin文件
    fun buildDialDfu(
        dialInfo: DfuDialInfo,
        binList: List<BinInfo>
    ): ByteArray {
//        val img_dial_thumbnail_Byte = img_dial_thumbnail.readBytes()
//        val img_dial_background_Byte = img_dial_background.readBytes()
        var offset = 0
        val headBytes = ByteArray(HEAD_INFO_LENGTH)
        val headFileBytes = ByteArray(HEAD_SUBFILE_INFO_LENGTH * binList.size)
        var binAddr = headBytes.size + headFileBytes.size
        for (i in binList.indices) {
            val bin = binList[i]
            var binOffset = i * HEAD_SUBFILE_INFO_LENGTH
            headFileBytes.writeInt32(binOffset, bin.crc16)
            binOffset += 4

            headFileBytes.writeInt32(binOffset, binAddr)
            binOffset += 4

            headFileBytes.writeInt32(binOffset, bin.size)
            binOffset += 4

            headFileBytes.writeUtf8String(binOffset, HEAD_SUBFILE_NAME_MAX, bin.name)
            binOffset += HEAD_SUBFILE_NAME_MAX

            binAddr += bin.size
        }
        val footerBytes = ByteArray(FOOTER_INFO_LENGTH)
        footerBytes.writeInt32(0, FOOTER_MAGIC.toInt())
        footerBytes.writeUtf8String(4, HEAD_VERSION_MAX, dialInfo.versionName)

//uint32_t magic;
        headBytes.writeInt32(offset, HEAD_MAGIC)
        offset += 4
//uint32_t headSize;
        headBytes.writeInt32(offset, headBytes.size + headFileBytes.size)
        offset += 4

//uint32_t footerSize;
        headBytes.writeInt32(offset, footerBytes.size)
        offset += 4
//uint8_t name[HEAD_FILE_NAME_MAX];
        headBytes.writeUtf8String(offset, HEAD_FILE_NAME_MAX, dialInfo.name);
        offset += HEAD_FILE_NAME_MAX
        //uint8_t author[HEAD_AUTHOR_MAX]; //bin 文件授权信息
        headBytes.writeUtf8String(offset, HEAD_AUTHOR_MAX, dialInfo.author);
        offset += HEAD_AUTHOR_MAX
//uint8_t version[HEAD_VERSION_MAX]; //bin 文件版本号
        headBytes.writeUtf8String(offset, HEAD_VERSION_MAX, dialInfo.versionName);
        offset += HEAD_VERSION_MAX
//uint32_t subcode; //bin 文件子版本号(递加版本)
        headBytes.writeInt32(offset, dialInfo.subVersion)
        offset += 4
//uint32_t crc16; //bin 文件校验码
        headBytes.writeInt32(offset, dialInfo.crc16)
        offset += 4
//uint32_t length; //bin 文件中所有图片数据的累加长度
        var length = 0
        binList.forEach {
            length += it.binBytes!!.size
        }
        headBytes.writeInt32(offset, length)
        offset += 4

//uint32_t fileNum; //bin 文件中图片的个数,同时也表示有多少个文件头
        headBytes.writeInt32(offset, binList.size)
        offset += 4
//ID
        headBytes.writeInt32(offset, dialInfo.id)
        offset += 4

        //color
        headBytes.writeInt32(offset, dialInfo.color)
        offset += 4

        //style
        headBytes.writeInt32(offset, dialInfo.style)
        offset += 4


        val dfuBytes = ByteArray(headBytes.size + headFileBytes.size + footerBytes.size + length)
        var copyOffset = 0
        System.arraycopy(headBytes, 0, dfuBytes, copyOffset, headBytes.size)
        copyOffset += headBytes.size
        System.arraycopy(headFileBytes, 0, dfuBytes, copyOffset, headFileBytes.size)
        copyOffset += headFileBytes.size
        for (i in binList.indices) {
            val bin = binList[i]
            System.arraycopy(bin.binBytes!!, 0, dfuBytes, copyOffset, bin.binBytes!!.size)
            copyOffset += bin.binBytes!!.size
        }
        System.arraycopy(footerBytes, 0, dfuBytes, copyOffset, footerBytes.size)
        copyOffset += footerBytes.size
        return dfuBytes
    }


    class DfuDialInfo {
        var id: Int = 0
        var color: Int = 0
        var style: Int = 0
        var name: String = "ls17_custom_dial.dfu"
        var author: String = "haylou_android"

        //格式是ver_2023-6-28 17:39:58
        var versionName: String = ""

        //子版本号
        var subVersion: Int = 0;
        var crc16: Int = 0;

    }

    class DfuInfo() {
        var name: String = ""
        var author: String = ""
        var version: String = ""
        var subCode: Int = 0
        var crc16: Int = 0
        var length: Int = 0
        var reserve: ByteArray? = null
        var footerVersion: String = ""
        var binInfos: List<BinInfo>? = null
        override fun toString(): String {
            return "DfuInfo(name='$name', author='$author', version='$version', subCode=$subCode, crc16=$crc16, length=$length, reserve=${reserve?.contentToString()}, footerVersion='$footerVersion', binInfos=$binInfos)"
        }


    }


    data class BinInfo(
        var crc16: Int = 0,
        var addr: Int = 0,
        var size: Int = 0,
        //要带png  背景图命名是 img_dial_background.png 缩略图命名是 img_dial_thumbnail.png
        var name: String = "",
        //index8格式的图片  Index8ImageUtils .getIndex8   图片需要通过 ImageMagic  转换成256色的图片
        var binBytes: ByteArray? = null
    ) {
        override fun equals(other: Any?): Boolean {
            if (this === other) return true
            if (javaClass != other?.javaClass) return false

            other as BinInfo

            if (crc16 != other.crc16) return false
            if (addr != other.addr) return false
            if (size != other.size) return false
            if (name != other.name) return false

            return true
        }

        override fun hashCode(): Int {
            var result = crc16
            result = 31 * result + addr
            result = 31 * result + size
            result = 31 * result + name.hashCode()
            return result
        }

        override fun toString(): String {
            return "BinInfo(crc16=$crc16, addr=$addr, size=$size, name='$name', binBytesSize=${binBytes?.size})"
        }

    }

    fun ByteArray.readUint32(offset: Int): Int {
        val i3 = this[offset + 3].toInt() and 0xFF shl 24
        val i2 = this[offset + 2].toInt() and 0xFF shl 16
        val i1 = this[offset + 1].toInt() and 0xFF shl 8
        val i0 = this[offset + 0].toInt() and 0xFF
        return (i3 or i2 or i1 or i0);
    }

    fun ByteArray.readUint32Little(offset: Int): Int {
        val i3 = this[offset + 0].toInt() and 0xFF shl 24
        val i2 = this[offset + 1].toInt() and 0xFF shl 16
        val i1 = this[offset + 2].toInt() and 0xFF shl 8
        val i0 = this[offset + 3].toInt() and 0xFF
        return (i3 or i2 or i1 or i0);
    }

    val HEX_ARRAY = "0123456789ABCDEF".toCharArray()
    private fun ByteArray.toHex(offset: Int, len: Int): String {
        val out = CharArray(len * 2)
        for (j in 0 until len) {
            val v = this[offset + j].toInt() and 0xFF
            out[j * 2] = HEX_ARRAY[v ushr 4]
            out[j * 2 + 1] = HEX_ARRAY[v and 0x0F]
        }
        return String(out)
    }

    private fun ByteArray.toUtf8String(offset: Int, len: Int): String {
        val out = ByteArray(len)
        for (j in 0 until len) {
            out[j] = this[j + offset]
        }
        return String(out)
    }

    private fun ByteArray.readBytes(offset: Int, len: Int): ByteArray {
        val out = ByteArray(len)
        for (j in 0 until len) {
            out[j] = this[j + offset]
        }
        return out
    }

    private fun ByteArray.writeUtf8String(offset: Int, len: Int, text: String) {
        val textBytes = text.toByteArray(Charsets.UTF_8)
        val max = Math.min(textBytes.size, len)
        for (i in 0 until max) {
            this[i + offset] = textBytes[i]
        }
    }

    private fun ByteArray.writeInt32(offset: Int, value: Int) {
        this[offset + 3] = (value shr 24 and 0xFF).toByte()
        this[offset + 2] = (value shr 16 and 0xFF).toByte()
        this[offset + 1] = (value shr 8 and 0xFF).toByte()
        this[offset] = (value and 0xFF).toByte()
    }
}