package com.besall.allbase.bluetooth;

public class BluetoothConstants {

    public class Scan {
        public static final String           BES_SCAN_IS_MULTIPLE_DEVICES = "BES_SCAN_IS_MULTIPLE_DEVICES";
        public static final String                               BES_SCAN = "BES_SCAN";
        public static final String                        BES_SCAN_RESULT = "BES_SCAN_RESULT";

        public static final int                                  SCAN_SPP = 0x000003E0;
        public static final int                                  SCAN_BLE = 0x000003E1;
        public static final int                                  SCAN_USB = 0x000003E2;

        public static final int                         REQUEST_CODE_SCAN = 0x000002E0;
        public static final String                 CHOOSE_WIFI_RESULT_KEY = "CHOOSE_WIFI_RESULT_KEY";

        public static final int               REQUEST_CODE_CAPTURE_CAMEIA = 0x000002E1;
        public static final int                 REQUEST_CODE_CHOOSE_PHOTO = 0x000002E2;
    }

    public class Connect {
        public static final byte                              CMD_CONFIRM = 0x01 ;
    }


}
