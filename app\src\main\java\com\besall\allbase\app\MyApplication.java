package com.besall.allbase.app;

import android.app.Application;
import android.content.Context;
import android.text.format.DateFormat;
import android.util.Log;

import com.bes.bessdk.utils.LogUtils;
import com.bes.sdk.core.OtaLog;
import com.besall.allbase.common.Initialize.InitializeUtils;

/**
 * Created by fanyu on 2019-04-18
 */
public class MyApplication extends Application {

    private static MyApplication instance;
    private Context applicationContext;
    CrashExceptionHandler crashExceptionHandler ;

    public static MyApplication getInstance() {
        return instance;
    }
    public static MyApplication getContext() {
        return instance;
    }

    @Override
    public void onCreate() {
        super.onCreate();
        instance = this;

        applicationContext = getApplicationContext();
        crashExceptionHandler = CrashExceptionHandler.getInstance();
        crashExceptionHandler.init(applicationContext);
        // 执行初始化操作
        InitializeUtils.init();
        OtaLog.init(applicationContext.getExternalFilesDir("log").getAbsolutePath(), 2);
        OtaLog.setLogger(new OtaLog.ILogger() {
            @Override
            public void logI(String text) {
                Log.i("sinyi", text);
            }

            @Override
            public void logE(String text) {
                Log.e("sinyi", text);
            }
        });

        LogUtils.initLogFile(String.valueOf(DateFormat.format("yyyy-MM-dd-HH-mm-ss-SSS", System.currentTimeMillis())));
    }

}
