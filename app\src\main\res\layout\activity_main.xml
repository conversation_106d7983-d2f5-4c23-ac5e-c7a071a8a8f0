<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    android:background="@color/white"
    tools:context=".view.base.BaseActivity"
    >

<!--    <androidx.appcompat.widget.Toolbar-->
<!--        android:id="@+id/toolbar"-->
<!--        android:layout_width="match_parent"-->
<!--        android:layout_height="50dp"-->
<!--        android:layout_marginTop="54dp"-->
<!--        android:background="?attr/colorAccent"-->
<!--        android:minHeight="?attr/actionBarSize"-->
<!--        android:theme="@style/titlebartheme.ToolBar"-->
<!--        android:layout_gravity="center" />-->

    <ImageView
        android:layout_width="320dp"
        android:layout_height="128dp"
        android:layout_marginTop="108dp"
        android:layout_gravity="center_horizontal"
        android:background="@drawable/logo"/>

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="210dp"
        android:layout_gravity="center">

        <Button
            android:id="@+id/entry_btn"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/entry_click"
            >

        </Button>
        <Button
            android:id="@+id/testOTABtn"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="OTA_TEST"
            >

        </Button>
    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginBottom="50dp">

    </View>

    <LinearLayout
        android:layout_marginTop="-80dp"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:gravity="center"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/version_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="13sp"
            android:textColor="@color/title_color_light"
            >
        </TextView>


        <Button
            android:id="@+id/privacy_policy"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/privacy_policy"
            android:textColor="@color/ff087ec2"
            android:background="@null"
            android:textAllCaps="false">

        </Button>
    </LinearLayout>

    <LinearLayout
        android:id="@+id/agree_view"
        android:layout_width="match_parent"
        android:layout_height="400dp"
        android:layout_marginLeft="50dp"
        android:layout_marginRight="50dp"
        android:layout_marginTop="-500dp"
        android:background="@color/white"
        android:orientation="vertical"
        android:visibility="gone"
        >

        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:paddingBottom="60dp"
            android:background="@color/lineview"
            >

            <TextView
                android:id="@+id/agreeTV"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:padding="10dp">

            </TextView>

        </ScrollView>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="-60dp"
            android:orientation="horizontal"
            android:gravity="center">
            <Button
                android:id="@+id/disagree"
                android:layout_width="80dp"
                android:layout_height="40dp"
                android:padding="10dp"
                android:text="@string/disagree"
                android:background="@color/white"
                android:textAllCaps="false">

            </Button>

            <View
                android:layout_width="20dp"
                android:layout_height="40dp">

            </View>

            <Button
                android:id="@+id/agree"
                android:layout_width="80dp"
                android:layout_height="40dp"
                android:padding="10dp"
                android:text="@string/agree"
                android:textColor="@color/ff087ec2"
                android:background="@color/white"
                android:textAllCaps="false">

            </Button>

        </LinearLayout>

    </LinearLayout>

</LinearLayout>