package com.besall.allbase.view.activity.chipstoollevel4.ota;

import android.content.Context;
import android.content.Intent;

import com.bes.bessdk.service.BesOtaService;
import com.bes.bessdk.service.base.BesServiceConfig;
import com.bes.bessdk.service.base.BesServiceListener;
import com.bes.sdk.ota.OTADfuInfo;
import com.bes.sdk.ota.OTATask;
import com.bes.sdk.ota.RemoteOTAConfig;
import com.besall.allbase.bluetooth.BluetoothConstants;
import com.besall.allbase.bluetooth.scan.ScanActivity;
import com.bes.bessdk.service.customerdial.CustomerDialService;
import com.besall.allbase.common.utils.ActivityUtils;
import com.besall.allbase.view.activity.tools.SettingActivity.SettingActivity;
import com.besall.allbase.view.base.BasePresenter;

import java.util.ArrayList;

public class Ota<PERSON>Presenter extends BasePresenter<IOtaUIActivity> implements IOtaUIPresenter {
    ArrayList<String> mFilePaths;
    ArrayList<OTATask> mOtaTasks = new ArrayList<>();

    CustomerDialService dialService;

    @Override
    public void pickDecice(OtaUIActivity context, int scan) {
        Intent intent = new Intent();
        intent.putExtra(BluetoothConstants.Scan.BES_SCAN, scan);
        intent.putExtra(BluetoothConstants.Scan.BES_SCAN_IS_MULTIPLE_DEVICES, true);
        ActivityUtils.gotoActForResult(intent, BluetoothConstants.Scan.REQUEST_CODE_SCAN, context, ScanActivity.class);
    }

    @Override
    public void connectDevice(BesServiceConfig serviceConfig, BesServiceListener listener, Context context, int index) {
        BesOtaService besOtaService = new BesOtaService(serviceConfig, listener, context);
        OTATask otaTask = besOtaService;
        mOtaTasks.add(index, otaTask);
    }

    public void connectCustomerDialDevice(BesServiceConfig serviceConfig, BesServiceListener listener, Context context) {
        dialService = new CustomerDialService(serviceConfig, listener, context);
    }

    public void startCustomerDialTransfer(byte[] oldData, byte[] data, int type, int isIncremental, byte[] param) {
        if (dialService != null)
            dialService.startTransfer(oldData, data, type, isIncremental, param);
    }

    @Override
    public void disconnectCustomerDialService() {
        if (dialService != null)
            dialService.disconnected();
    }


    @Override
    public void onPickOtaFile(String path, int index) {
        if (mFilePaths == null) {
            mFilePaths = new ArrayList<>();
        }
        if (mFilePaths.size() > index) {
            mFilePaths.remove(index);
        }
        mFilePaths.add(index, path);
    }

    @Override
    public void startOta(OtaUIActivity context, int breakpoint, int index) {
        if (mOtaTasks.get(index) == null || mFilePaths.get(index) == null) return;
        RemoteOTAConfig config = new RemoteOTAConfig();
        config.setLocalPath(mFilePaths.get(index));
        mOtaTasks.get(index).setOtaConfig(config);
        OTADfuInfo otaDfuInfo = new OTADfuInfo("001", breakpoint);
        mOtaTasks.get(index).startDataTransfer(otaDfuInfo, context);
    }

    @Override
    public void stopOta() {
        for (int i = 0; i < mOtaTasks.size(); i ++) {
            OTATask otaTask = mOtaTasks.get(i);
            if (otaTask != null) {
                otaTask.stopDataTransfer();
                otaTask = null;
            }
        }
    }

    @Override
    public void goToSettingActivity(OtaUIActivity activity) {
        ActivityUtils.gotoAct(new Intent(), activity, SettingActivity.class);
    }
}
