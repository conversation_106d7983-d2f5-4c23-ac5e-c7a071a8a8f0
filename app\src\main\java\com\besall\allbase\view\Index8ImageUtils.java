package com.besall.allbase.view;

import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Color;
import android.util.Log;

import com.bes.bessdk.utils.ArrayUtil;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/6/28 17:48
 */

public class Index8ImageUtils {
    public static final String TAG="Index8";
    public static byte[] getIndex8(String path){
        BitmapFactory.Options options = new BitmapFactory.Options();
        Log.i(TAG, "getIndex8: path---" + path);
        options.inPreferredConfig = Bitmap.Config.ARGB_8888;
        Bitmap bitmap = BitmapFactory.decodeFile(path, options);
        return getIndex8(bitmap);
    }



    public static byte[] getIndex8(Bitmap bitmap) {
        int width = bitmap.getWidth();
        int height = bitmap.getHeight();
        int colorTableSize = 256 * 4;
        byte[] header = new byte[16];
        String headerStr = "IDX8IDX8";
        byte[] header1 = ArrayUtil.toBytes(ArrayUtil.str2HexStr(headerStr));
        byte[] header2 = ArrayUtil.bytesSplic(ArrayUtil.intToBytes2(width), ArrayUtil.intToBytes2(height), ArrayUtil.intToBytes2(colorTableSize), ArrayUtil.intToBytes2(0));
        header = ArrayUtil.byteMerger(header1, header2);
        byte[] indexTab = new byte[width * height];
        byte[] colorTab = new byte[colorTableSize];
        byte[] data = new byte[header.length + indexTab.length + colorTab.length];
        Log.i(TAG, "getIndex8: path---000000");

        Map<String, Integer> colorMap = new HashMap<>();
        for (int i = 0; i < height; i ++) {
            for (int j = 0; j < width; j++) {
                int color = bitmap.getPixel(j, i);
                // float A = Color.alpha(color);//color.alpha();
                int intR = Color.red(color);// ()int) (color.red() * A);
                int intG = Color.green(color);//(int) (color.green() * A);
                int intB = Color.blue(color);// (int) (color.blue() * A);
                int intA = Color.alpha(color);
                String key = intR + "," + intG + "," + intB + "," + intA;
                if (colorMap.size() < 256 && !colorMap.containsKey(key)) {
                    colorMap.put(key, Integer.valueOf(colorMap.size()));
                }
                if (colorMap.size() == 256) {
                    break;
                }
            }
        }
        Log.i(TAG, "getIndex8: -----" + colorMap.size());
        for (int i = 0; i < height; i++) {
            for (int j = 0; j < width; j++) {
                int color = bitmap.getPixel(j, i);
                float A = Color.alpha(color);//color.alpha();
                int intR = Color.red(color);// ()int) (color.red() * A);
                int intG = Color.green(color);//(int) (color.green() * A);
                int intB = Color.blue(color);// (int) (color.blue() * A);
                int intA = (int) A;
                String key = intR + "," + intG + "," + intB + "," + intA;
                Integer value = colorMap.get(key);
                if (value != null) {
                    indexTab[i * width + j] = (byte) value.intValue();
                }
            }
        }
        Log.i(TAG, "getIndex8: path---111111");
        for (Map.Entry<String, Integer> entry : colorMap.entrySet()) {
            int index = entry.getValue();
            String[] colorArr = entry.getKey().split(",");
//            Log.i(TAG, "getIndex8: -----" + index);
//            Log.i(TAG, "colorArr: --------" + colorArr[0] + "---" + colorArr[1] + "---" + colorArr[2] + "---" + colorArr[3]);
            byte[] colorByte = new byte[]{(byte) Integer.valueOf(colorArr[0]).intValue(), (byte) Integer.valueOf(colorArr[1]).intValue(), (byte) Integer.valueOf(colorArr[2]).intValue(), (byte) Integer.valueOf(colorArr[3]).intValue()};
//            Log.i(TAG, "colorByte: --------" + colorByte[0] + "---" + colorByte[1] + "---" + colorByte[2] + "---" + colorByte[3]);
            colorTab[index * 4] = colorByte[0];
            colorTab[index * 4 + 1] = colorByte[1];
            colorTab[index * 4 + 2] = colorByte[2];
            colorTab[index * 4 + 3] = colorByte[3];
        }
        System.arraycopy(header, 0, data, 0, header.length);
        System.arraycopy(indexTab, 0, data, header.length, indexTab.length);
        System.arraycopy(colorTab, 0, data, header.length + indexTab.length, colorTab.length);
        return data;
    }
}
