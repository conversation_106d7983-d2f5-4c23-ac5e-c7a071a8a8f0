package com.besall.allbase.view.activity.level3;


import android.content.Intent;
import android.view.MenuItem;
import android.view.View;
import android.widget.Button;
import android.widget.TextView;

import androidx.appcompat.widget.Toolbar;

import com.besall.allbase.R;
import com.besall.allbase.common.utils.ActivityUtils;

import com.besall.allbase.view.activity.chipstoollevel4.checkcrc.CheckCrcActivity;
import com.besall.allbase.view.activity.chipstoollevel4.commandset.CommandSetActivity;
import com.besall.allbase.view.activity.chipstoollevel4.customercmd.CustomCmdActivity;
import com.besall.allbase.view.base.BaseActivity;

/**
 * <AUTHOR>
 * @time $ $
 */
public class FunctionToolsActivity extends BaseActivity<IFunctionToolsActivity, FunctionToolsPresenter> implements IFunctionToolsActivity, View.OnClickListener {

    private static FunctionToolsActivity instance;

    private Button avslwa;
    private Button rssi;
    private Button rssi_extend;
    private Button audio_dump;
    private Button log_dump;
    private Button crash_dump;
    private Button custom_command;
//    private Button opus_audio_dump;
    private Button ble_wifi;
    private Button EQ;
    private Button capsensor;
    private Button throughput;
    private Button check_crc;
    private Button command_set;
    private Button auracast_assistant;




    @Override
    protected FunctionToolsPresenter createPresenter() {
        return new FunctionToolsPresenter();
    }

    @Override
    protected void initBeforeSetContent() {

    }

    @Override
    protected int getContentViewId() {
        return R.layout.activity_chipstollfunction;
    }

    @Override
    protected void bindView() {
        custom_command = (Button)findViewById(R.id.custom_command);

//        opus_audio_dump = (Button)findViewById(R.id.opus_audio_dump);
        tv_title = (TextView) findViewById(R.id.tv_title);
        check_crc = (Button)findViewById(R.id.check_crc);
        command_set = (Button)findViewById(R.id.command_set);
        mToolbar = (Toolbar) findViewById(R.id.toolbar);
    }

    @Override
    protected void initView() {
        custom_command.setOnClickListener(instance);
        check_crc.setOnClickListener(instance);
        command_set.setOnClickListener(instance);
        tv_title.setText("BES CHIPS TOOLS");
        mToolbar.setTitle("");
        setSupportActionBar(mToolbar);
        getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        getSupportActionBar().setHomeButtonEnabled(true);

    }

    @Override
    protected void setInstance() {
        instance = this;
    }

    @Override
    protected void removeInstance() {
        instance = null;
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        switch (item.getItemId()) {
            case android.R.id.home:
                finish();
                break;
        }
        return super.onOptionsItemSelected(item);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.custom_command:
                ActivityUtils.gotoAct(new Intent(), instance, CustomCmdActivity.class);
                break;
            case R.id.check_crc:
                ActivityUtils.gotoAct(new Intent(),instance, CheckCrcActivity.class);
                break;
            case R.id.command_set:
                ActivityUtils.gotoAct(new Intent(),instance, CommandSetActivity.class);
                break;
            default:
                break;
        }
    }
}
