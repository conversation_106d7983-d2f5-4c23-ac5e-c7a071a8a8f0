package com.bes.sdk.utils;

/**
 * Enum of device stereo channel.
 */
public enum AudioChannel
{
    // Channel info.
    UNKNOWN         (-1,    "Unknown"),
    NONE_CHANNEL    (0,     "NONE_CHANNEL"),
    STEREO_LEFT     (1,     "LEFT_CHANNEL"),
    STEREO_RIGHT    (2,     "RIGHT_CHANNEL");

    private int mValue;
    private String mName;

    AudioChannel(int value, String name)
    {
        mValue = value;
        mName = name;
    }

    public static AudioChannel getChannel(int channel)
    {
        switch (channel) {
            case 0:
                return NONE_CHANNEL;
            case 1:
                return STEREO_LEFT;
            case 2:
                return STEREO_RIGHT;
        }
        return UNKNOWN;
    }

    public int getValue() {
        return mValue;
    }

    public String getName() {
        return mName;
    }

    @Override
    public String toString() {
        return "AudioChannel{" +
                "mValue=" + mValue +
                ", mName='" + mName + '\'' +
                '}';
    }
}
