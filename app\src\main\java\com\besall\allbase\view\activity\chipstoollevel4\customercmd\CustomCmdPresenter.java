package com.besall.allbase.view.activity.chipstoollevel4.customercmd;

import static com.besall.allbase.common.Constants.FILE_CODE;

import android.content.Context;
import android.content.Intent;

import com.bes.bessdk.service.base.BesServiceConfig;
import com.bes.bessdk.service.base.BesServiceListener;
import com.bes.bessdk.service.customcmd.CustomCmdService;
import com.besall.allbase.bluetooth.BluetoothConstants;
import com.besall.allbase.bluetooth.scan.ScanActivity;
import com.besall.allbase.common.utils.ActivityUtils;
import com.besall.allbase.view.activity.tools.FileActivity.CustomCmdFilelistActivity;
import com.besall.allbase.view.activity.tools.FileActivity.FilelistActivity;
import com.besall.allbase.view.base.BasePresenter;

/**
 * <AUTHOR>
 * @time $ $
 */
class CustomCmdPresenter extends BasePresenter<ICustomCmdActivity> implements ICustomCmdPresenter {

    private CustomCmdService customCmdService;

    static String TAG = "CustomCmdPresenter";
    @Override
    public void pickDecice(CustomCmdActivity context, int scan) {
        Intent intent = new Intent();
        intent.putExtra(BluetoothConstants.Scan.BES_SCAN, scan);
        ActivityUtils.gotoActForResult(intent, BluetoothConstants.Scan.REQUEST_CODE_SCAN, context, ScanActivity.class);
    }

    @Override
    public void connectDevice(BesServiceConfig serviceConfig, BesServiceListener listener, Context context) {
        customCmdService = new CustomCmdService(serviceConfig, listener, context);
    }

    @Override
    public void sendCustomCmd(String cmd, int i) {
        if (customCmdService != null) {
            customCmdService.sendCustomCmd(cmd, i);
        }
    }

    @Override
    public void sendCustomCommand(byte[] data) {
        if (customCmdService != null) {
            customCmdService.sendCustomCommand(data);
        }
    }

    @Override
    public void stopSpp() {
        if (customCmdService != null) {
            customCmdService.disconnected();
        }
    }

    @Override
    public void importfile(CustomCmdActivity context, int json) {
        Intent intent = new Intent();
        ActivityUtils.gotoActForResult(intent, FILE_CODE, context, FilelistActivity.class);
    }

    @Override
    public void selectfile(CustomCmdActivity context, int file) {
        Intent intent = new Intent();
        ActivityUtils.gotoActForResult(intent, FILE_CODE, context, CustomCmdFilelistActivity.class);
    }


}
