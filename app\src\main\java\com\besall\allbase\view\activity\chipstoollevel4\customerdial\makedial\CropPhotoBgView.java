package com.besall.allbase.view.activity.chipstoollevel4.customerdial.makedial;

import static android.graphics.Region.Op.DIFFERENCE;
import static com.bes.bessdk.service.customerdial.CustomerDialConstants.CUSTOMER_DIAL_CROP_PHOTO_FORM_KEY;
import static com.bes.bessdk.service.customerdial.CustomerDialConstants.CUSTOMER_DIAL_CROP_PHOTO_FORM_VALUE;
import static com.bes.bessdk.service.customerdial.CustomerDialConstants.CUSTOMER_DIAL_CROP_PHOTO_LENGTH_KEY;
import static com.bes.bessdk.service.customerdial.CustomerDialConstants.CUSTOMER_DIAL_CROP_PHOTO_LENGTH_VALUE;
import static com.bes.bessdk.service.customerdial.CustomerDialConstants.CUSTOMER_DIAL_CROP_PHOTO_WIDTH_KEY;
import static com.bes.bessdk.service.customerdial.CustomerDialConstants.CUSTOMER_DIAL_CROP_PHOTO_WIDTH_VALUE;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.Path;
import android.graphics.RectF;
import android.graphics.drawable.Drawable;
import android.util.AttributeSet;
import android.util.Log;
import android.view.View;

import androidx.annotation.Nullable;

import com.bes.bessdk.utils.SPHelper;

public class CropPhotoBgView extends View {

    private Drawable bgDrawable;
    private Context mContext;

    public CropPhotoBgView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        mContext = context;
    }
    public CropPhotoBgView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs, 0);
        mContext = context;
    }
    public CropPhotoBgView(Context context) {
        super(context);
        mContext = context;
    }

    @Override
    protected void onDraw(Canvas canvas) {
        Log.i("TAG", "onDraw: ---------canvas");
        Paint paint = new Paint();
        paint.setColor(Color.BLACK);
        paint.setXfermode(null);
        canvas.saveLayer(new RectF(0, 0, canvas.getWidth(), canvas.getHeight()), paint, Canvas.ALL_SAVE_FLAG);
        Path path = new Path();
        int length = (int) SPHelper.getPreference(mContext, CUSTOMER_DIAL_CROP_PHOTO_LENGTH_KEY, CUSTOMER_DIAL_CROP_PHOTO_LENGTH_VALUE);
        int width = (int) SPHelper.getPreference(mContext, CUSTOMER_DIAL_CROP_PHOTO_WIDTH_KEY, CUSTOMER_DIAL_CROP_PHOTO_WIDTH_VALUE);
        int rectW = width;
        int rectH = length;
        int photoForm = (int) SPHelper.getPreference(mContext, CUSTOMER_DIAL_CROP_PHOTO_FORM_KEY, CUSTOMER_DIAL_CROP_PHOTO_FORM_VALUE);
        if (photoForm == 2) {
            path.addRect(getWidth() / 2 - rectW, getHeight() / 2 - rectH, getWidth() / 2 + rectW, getHeight() / 2 + rectH, Path.Direction.CW);
        } else {
            path.addCircle(getWidth() / 2, getHeight() / 2, rectH, Path.Direction.CW);
        }
        canvas.clipPath(path, DIFFERENCE);
        drawBackground(canvas);

        super.onDraw(canvas);
    }

    @Override
    public void setBackground(Drawable background) {
        bgDrawable = background;
        invalidate();
    }

    @Override
    public void setBackgroundDrawable(@Nullable Drawable background) {
        setBackground(background);
    }

    @Override
    public void setBackgroundResource(int resId) {
        setBackground(getContext().getResources().getDrawable(resId));
    }

    /**
     * 自己绘制背景
     * @param canvas
     */
    private void drawBackground(Canvas canvas) {
        final Drawable background = bgDrawable;
        if (background == null) {
            return;
        }

        background.setBounds(0, 0, getRight() - getLeft(), getBottom() - getTop());

        background.draw(canvas);
    }

}
