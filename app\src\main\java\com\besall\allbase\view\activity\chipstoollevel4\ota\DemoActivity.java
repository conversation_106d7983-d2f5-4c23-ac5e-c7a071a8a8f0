package com.besall.allbase.view.activity.chipstoollevel4.ota;

import static com.bes.bessdk.service.BesOTAConstants.OTA_CMD_GET_HW_INFO;
import static com.besall.allbase.bluetooth.BluetoothConstants.Scan.BES_SCAN_RESULT;

import android.bluetooth.BluetoothDevice;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;

import com.bes.bessdk.BesSdkConstants;
import com.bes.bessdk.scan.BtHeleper;
import com.bes.bessdk.service.BesOtaService;
import com.bes.bessdk.service.base.BesServiceConfig;
import com.bes.bessdk.service.base.BesServiceListener;

import com.bes.sdk.core.OtaLog;
import com.bes.sdk.device.HmDevice;
import com.bes.sdk.ota.OTADfuInfo;
import com.bes.sdk.ota.OTATask;
import com.bes.sdk.ota.RemoteOTAConfig;
import com.bes.sdk.utils.DeviceProtocol;
import com.bes.sdk.utils.OTAStatus;
import com.besall.allbase.R;
import com.besall.allbase.bluetooth.BluetoothConstants;
import com.besall.allbase.bluetooth.scan.ScanActivity;
import com.besall.allbase.common.utils.ActivityUtils;

/**
 * <AUTHOR>
 * @date 2023/6/21 17:28
 */

public class DemoActivity extends AppCompatActivity implements BesServiceListener, OTATask.StatusListener {

    BluetoothDevice mDevice;
    HmDevice mHmDevice;

    OTATask otaTask;
    BesServiceConfig mServiceConfig;
    View btDeviceSelect;

    TextView tvDevice;
    TextView tvFileSelect;
    View tvOtaUpdate;
    TextView tvState;
    TextView tvLog;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_demo);

        btDeviceSelect = findViewById(R.id.btDeviceSelect);
        tvDevice = findViewById(R.id.tvDevice);
        tvFileSelect = findViewById(R.id.tvFileSelect);
        //⚠️⚠️⚠️⚠️⚠️⚠️⚠️⚠️Change the path to your path
        tvFileSelect.setText("/storage/emulated/0/Android/data/com.bes.besall/test.bin");
        ///storage/emulated/0/Android/data/com.bes.besall/files/bin/test.bin

        tvOtaUpdate = findViewById(R.id.tvOtaUpdate);
        tvState = findViewById(R.id.tvState);
        tvLog = findViewById(R.id.tvLog);
        OtaLog.logI("DemoActivity onCreate");

        btDeviceSelect.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Intent intent = new Intent();
                intent.putExtra(BluetoothConstants.Scan.BES_SCAN, BluetoothConstants.Scan.SCAN_SPP);
                ActivityUtils.gotoActForResult(intent, BluetoothConstants.Scan.REQUEST_CODE_SCAN, DemoActivity.this, ScanActivity.class);
            }
        });

        tvOtaUpdate.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                //⚠️⚠️⚠️⚠️⚠️⚠️⚠️⚠️init ServiceConfig first
                mServiceConfig = new BesServiceConfig();
//                mServiceConfig.setDeviceProtocol(DeviceProtocol.PROTOCOL_BLE);
//                mServiceConfig.setServiceUUID(BesSdkConstants.BES_OTA_SERVICE_OTA_UUID);
//                mServiceConfig.setCharacteristicsUUID(BesSdkConstants.BES_OTA_CHARACTERISTIC_OTA_UUID);
//                mServiceConfig.setDescriptorUUID(BesSdkConstants.BES_OTA_DESCRIPTOR_OTA_UUID);

                mServiceConfig.setDeviceProtocol(DeviceProtocol.PROTOCOL_SPP);
                mServiceConfig.setServiceUUID(BesSdkConstants.BES_OTA_SERVICE_OTA_UUID);
                mServiceConfig.setUseTotaV2(false);
                mServiceConfig.setTotaConnect(false);
                mServiceConfig.setUSER_FLAG(1);
                mServiceConfig.setCurUser(1);
                mServiceConfig.setDevice(mHmDevice);
                BesOtaService besOtaService = new BesOtaService(mServiceConfig, DemoActivity.this, DemoActivity.this);
                otaTask = besOtaService;
            }
        });
        findViewById(R.id.tvOtaUpdateCancel).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
//                BesOtaManager.getInstance().interruptUpgrade();
            }
        });

    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == BluetoothConstants.Scan.REQUEST_CODE_SCAN) {
            onPickDevice(resultCode, data);
        }
    }

    private void onPickDevice(int resultCode, Intent data) {
        if (resultCode == RESULT_OK) {
            mHmDevice = (HmDevice) data.getSerializableExtra(BES_SCAN_RESULT);
            mDevice = BtHeleper.getBluetoothAdapter(this).getRemoteDevice(mHmDevice.getPreferredProtocol() == DeviceProtocol.PROTOCOL_BLE ? mHmDevice.getBleAddress() : mHmDevice.getDeviceMAC());
            tvDevice.setText(mDevice.getName() + "   " + mDevice.getAddress());
            tvDevice.setTextColor(getColor(R.color.btnDisableColor));
        }
    }

    @Override
    public void onBackPressed() {
        super.onBackPressed();
        finish();
    }

    @Override
    public void onTotaConnectState(boolean state, HmDevice hmDevice) {

    }

    @Override
    public void onErrorMessage(int msg, HmDevice hmDevice) {

    }

    @Override
    public void onStateChangedMessage(int msg, String msgStr, HmDevice hmDevice) {
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                switch (msg) {
                    case OTA_CMD_GET_HW_INFO://BES_CONNECT_SUCCESS:
                        //⚠️⚠️⚠️⚠️⚠️⚠️⚠️⚠️See OtaUIActivity for details
                        RemoteOTAConfig config = new RemoteOTAConfig();
                        config.setLocalPath(tvFileSelect.getText().toString());
                        otaTask.setOtaConfig(config);
                        OTADfuInfo otaDfuInfo = new OTADfuInfo("001", 0);//breakpoint为0则从0开始升级，为1则从断点处升级
                        otaTask.startDataTransfer(otaDfuInfo, DemoActivity.this);
                        break;
                    default:
                        break;
                }
            }
        });
    }

    @Override
    public void onSuccessMessage(int msg, HmDevice hmDevice) {

    }

    @Override
    public void onOTAStatusChanged(OTAStatus newStatus, HmDevice hmDevice) {

    }

    @Override
    public void onOTAProgressChanged(float progress, HmDevice hmDevice) {

        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                tvLog.setText("progress-->" + progress);
            }
        });
    }
}
